#!/usr/bin/env python3
"""
Specialized Feature Engineering Techniques for BTK Datathon 2025
Focus on high-impact techniques for e-commerce session value prediction
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

class SpecializedFeatureEngineer:
    """
    High-impact feature engineering techniques
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.feature_stats = {}
        
    def create_funnel_features(self, df):
        """Create e-commerce funnel progression features"""
        print("Creating e-commerce funnel features...")
        
        # Sort by session and time
        df_sorted = df.sort_values(['user_session', 'event_time'])
        
        funnel_features = []
        
        for session_id, session_data in df_sorted.groupby('user_session'):
            features = {'user_session': session_id}
            
            events = session_data['event_type'].tolist()
            
            # Funnel stage indicators
            features['has_view'] = int('VIEW' in events)
            features['has_add_cart'] = int('ADD_CART' in events)
            features['has_remove_cart'] = int('REMOVE_CART' in events)
            features['has_buy'] = int('BUY' in events)
            
            # Funnel progression patterns
            features['view_to_cart'] = int('VIEW' in events and 'ADD_CART' in events)
            features['cart_to_buy'] = int('ADD_CART' in events and 'BUY' in events)
            features['complete_funnel'] = int(all(event in events for event in ['VIEW', 'ADD_CART', 'BUY']))
            
            # Funnel abandonment signals
            features['cart_abandonment'] = int('ADD_CART' in events and 'BUY' not in events)
            features['remove_after_add'] = int('ADD_CART' in events and 'REMOVE_CART' in events)
            
            # Funnel efficiency scores
            total_events = len(events)
            features['buy_event_ratio'] = events.count('BUY') / max(total_events, 1)
            features['cart_event_ratio'] = events.count('ADD_CART') / max(total_events, 1)
            features['view_event_ratio'] = events.count('VIEW') / max(total_events, 1)
            
            # Advanced funnel metrics
            if 'VIEW' in events and 'BUY' in events:
                view_indices = [i for i, e in enumerate(events) if e == 'VIEW']
                buy_indices = [i for i, e in enumerate(events) if e == 'BUY']
                features['view_to_buy_distance'] = min(buy_indices) - max(view_indices) if buy_indices and view_indices else 0
            else:
                features['view_to_buy_distance'] = 0
            
            # Hesitation indicators
            features['hesitation_score'] = events.count('REMOVE_CART') + events.count('VIEW') * 0.1
            
            funnel_features.append(features)
        
        return pd.DataFrame(funnel_features)
    
    def create_temporal_intensity_features(self, df):
        """Create temporal intensity and rhythm features"""
        print("Creating temporal intensity features...")

        # Ensure datetime conversion
        df = df.copy()
        df['event_time'] = pd.to_datetime(df['event_time'])

        df_sorted = df.sort_values(['user_session', 'event_time'])

        intensity_features = []

        for session_id, session_data in df_sorted.groupby('user_session'):
            features = {'user_session': session_id}

            if len(session_data) <= 1:
                # Single event session
                features.update({
                    'event_intensity': 0,
                    'rhythm_regularity': 0,
                    'burst_activity': 0,
                    'session_momentum': 0,
                    'peak_activity_hour': session_data['event_time'].iloc[0].hour,
                    'activity_span_hours': 0
                })
            else:
                # Calculate time differences
                times = session_data['event_time']
                time_diffs = times.diff().dt.total_seconds().dropna()

                # Event intensity (events per minute)
                session_duration = (times.max() - times.min()).total_seconds() / 60
                features['event_intensity'] = len(session_data) / max(session_duration, 1)

                # Rhythm regularity (coefficient of variation of time intervals)
                if len(time_diffs) > 0 and time_diffs.std() > 0:
                    features['rhythm_regularity'] = 1 / (1 + time_diffs.std() / time_diffs.mean())
                else:
                    features['rhythm_regularity'] = 1

                # Burst activity (short intervals indicating rapid interactions)
                burst_threshold = 30  # seconds
                features['burst_activity'] = (time_diffs < burst_threshold).sum() / len(time_diffs)

                # Session momentum (increasing activity over time)
                if len(session_data) >= 3:
                    first_half = len(session_data) // 2
                    first_half_events = first_half
                    second_half_events = len(session_data) - first_half
                    features['session_momentum'] = second_half_events / max(first_half_events, 1)
                else:
                    features['session_momentum'] = 1

                # Peak activity hour
                hour_counts = times.dt.hour.value_counts()
                features['peak_activity_hour'] = hour_counts.index[0]

                # Activity span
                features['activity_span_hours'] = (times.max() - times.min()).total_seconds() / 3600

            intensity_features.append(features)

        return pd.DataFrame(intensity_features)
    
    def create_product_affinity_features(self, df, is_train=True):
        """Create product and category affinity features"""
        print(f"Creating product affinity features for {'train' if is_train else 'test'} data...")
        
        if is_train:
            # Calculate product co-occurrence patterns
            session_products = df.groupby('user_session')['product_id'].apply(list).to_dict()
            
            # Product pair co-occurrences
            product_pairs = {}
            for products in session_products.values():
                unique_products = list(set(products))
                for i, prod1 in enumerate(unique_products):
                    for prod2 in unique_products[i+1:]:
                        pair = tuple(sorted([prod1, prod2]))
                        product_pairs[pair] = product_pairs.get(pair, 0) + 1
            
            self.feature_stats['product_pairs'] = product_pairs
            
            # Category affinity patterns
            category_sessions = df.groupby('user_session')['category_id'].apply(list).to_dict()
            category_diversity = {session: len(set(cats)) for session, cats in category_sessions.items()}
            self.feature_stats['avg_category_diversity'] = np.mean(list(category_diversity.values()))
        
        # Apply affinity features
        affinity_features = []
        
        for session_id, session_data in df.groupby('user_session'):
            features = {'user_session': session_id}
            
            products = session_data['product_id'].tolist()
            categories = session_data['category_id'].tolist()
            
            # Product diversity metrics
            unique_products = set(products)
            features['product_diversity'] = len(unique_products)
            features['product_repeat_rate'] = (len(products) - len(unique_products)) / max(len(products), 1)
            
            # Category exploration
            unique_categories = set(categories)
            features['category_diversity'] = len(unique_categories)
            features['category_focus'] = 1 / max(len(unique_categories), 1)  # Inverse of diversity
            
            # Cross-category shopping
            features['cross_category_shopping'] = int(len(unique_categories) > 1)
            
            # Product affinity score (based on training data)
            affinity_score = 0
            if hasattr(self, 'feature_stats') and 'product_pairs' in self.feature_stats:
                product_list = list(unique_products)
                for i, prod1 in enumerate(product_list):
                    for prod2 in product_list[i+1:]:
                        pair = tuple(sorted([prod1, prod2]))
                        affinity_score += self.feature_stats['product_pairs'].get(pair, 0)
            
            features['product_affinity_score'] = affinity_score
            
            affinity_features.append(features)
        
        return pd.DataFrame(affinity_features)
    
    def create_user_journey_features(self, df):
        """Create user journey and navigation pattern features"""
        print("Creating user journey features...")
        
        df_sorted = df.sort_values(['user_session', 'event_time'])
        
        journey_features = []
        
        for session_id, session_data in df_sorted.groupby('user_session'):
            features = {'user_session': session_id}
            
            events = session_data['event_type'].tolist()
            products = session_data['product_id'].tolist()
            categories = session_data['category_id'].tolist()
            
            # Journey length and complexity
            features['journey_length'] = len(events)
            features['unique_event_types'] = len(set(events))
            
            # Navigation patterns
            if len(events) > 1:
                # Event transitions
                transitions = [f"{events[i]}_{events[i+1]}" for i in range(len(events)-1)]
                features['unique_transitions'] = len(set(transitions))
                
                # Backtracking (going back to previous event types)
                backtrack_count = 0
                for i in range(2, len(events)):
                    if events[i] in events[:i-1]:
                        backtrack_count += 1
                features['backtracking_rate'] = backtrack_count / max(len(events)-2, 1)
                
                # Product switching
                product_switches = sum(1 for i in range(len(products)-1) if products[i] != products[i+1])
                features['product_switching_rate'] = product_switches / max(len(products)-1, 1)
                
                # Category switching
                category_switches = sum(1 for i in range(len(categories)-1) if categories[i] != categories[i+1])
                features['category_switching_rate'] = category_switches / max(len(categories)-1, 1)
            else:
                features.update({
                    'unique_transitions': 0,
                    'backtracking_rate': 0,
                    'product_switching_rate': 0,
                    'category_switching_rate': 0
                })
            
            # Journey efficiency
            if 'BUY' in events:
                buy_position = events.index('BUY')
                features['journey_efficiency'] = 1 / (buy_position + 1)  # Earlier buy = higher efficiency
            else:
                features['journey_efficiency'] = 0
            
            # Exploration vs exploitation
            exploration_events = ['VIEW']
            exploitation_events = ['ADD_CART', 'BUY']
            
            exploration_count = sum(1 for e in events if e in exploration_events)
            exploitation_count = sum(1 for e in events if e in exploitation_events)
            
            features['exploration_ratio'] = exploration_count / max(len(events), 1)
            features['exploitation_ratio'] = exploitation_count / max(len(events), 1)
            features['exploration_exploitation_balance'] = min(exploration_count, exploitation_count) / max(max(exploration_count, exploitation_count), 1)
            
            journey_features.append(features)
        
        return pd.DataFrame(journey_features)
    
    def create_value_prediction_features(self, df, is_train=True):
        """Create features specifically designed for value prediction"""
        print(f"Creating value prediction features for {'train' if is_train else 'test'} data...")
        
        if is_train and 'session_value' in df.columns:
            # Calculate value-based statistics for products and categories
            product_values = df.groupby('product_id')['session_value'].agg(['mean', 'std', 'count'])
            category_values = df.groupby('category_id')['session_value'].agg(['mean', 'std', 'count'])
            
            self.feature_stats['product_value_mean'] = product_values['mean'].to_dict()
            self.feature_stats['product_value_std'] = product_values['std'].fillna(0).to_dict()
            self.feature_stats['category_value_mean'] = category_values['mean'].to_dict()
            self.feature_stats['category_value_std'] = category_values['std'].fillna(0).to_dict()
            
            # Overall statistics
            self.feature_stats['global_value_mean'] = df['session_value'].mean()
            self.feature_stats['global_value_std'] = df['session_value'].std()
        
        value_features = []
        
        for session_id, session_data in df.groupby('user_session'):
            features = {'user_session': session_id}
            
            products = session_data['product_id'].tolist()
            categories = session_data['category_id'].tolist()
            events = session_data['event_type'].tolist()
            
            # Product value indicators
            if hasattr(self, 'feature_stats') and 'product_value_mean' in self.feature_stats:
                product_values = [self.feature_stats['product_value_mean'].get(p, self.feature_stats.get('global_value_mean', 0)) for p in products]
                features['avg_product_value_indicator'] = np.mean(product_values)
                features['max_product_value_indicator'] = np.max(product_values) if product_values else 0
                features['min_product_value_indicator'] = np.min(product_values) if product_values else 0
                features['product_value_range'] = features['max_product_value_indicator'] - features['min_product_value_indicator']
            else:
                features.update({
                    'avg_product_value_indicator': 0,
                    'max_product_value_indicator': 0,
                    'min_product_value_indicator': 0,
                    'product_value_range': 0
                })
            
            # Category value indicators
            if hasattr(self, 'feature_stats') and 'category_value_mean' in self.feature_stats:
                category_values = [self.feature_stats['category_value_mean'].get(c, self.feature_stats.get('global_value_mean', 0)) for c in categories]
                features['avg_category_value_indicator'] = np.mean(category_values)
                features['max_category_value_indicator'] = np.max(category_values) if category_values else 0
            else:
                features.update({
                    'avg_category_value_indicator': 0,
                    'max_category_value_indicator': 0
                })
            
            # Purchase intent signals
            features['purchase_intent_score'] = (
                events.count('BUY') * 10 +
                events.count('ADD_CART') * 3 +
                events.count('VIEW') * 1 -
                events.count('REMOVE_CART') * 2
            )
            
            # Value-driving behavior patterns
            features['high_value_behavior'] = int(
                events.count('BUY') > 0 and 
                len(set(products)) > 1 and
                len(events) >= 3
            )
            
            value_features.append(features)
        
        return pd.DataFrame(value_features)
    
    def apply_specialized_feature_engineering(self, train_df, test_df=None):
        """Apply all specialized feature engineering techniques"""
        print("\n" + "="*60)
        print("SPECIALIZED FEATURE ENGINEERING PIPELINE")
        print("="*60)
        
        results = {}
        
        # 1. E-commerce funnel features
        train_funnel = self.create_funnel_features(train_df)
        test_funnel = self.create_funnel_features(test_df) if test_df is not None else None
        results['funnel'] = (train_funnel, test_funnel)
        
        # 2. Temporal intensity features
        train_intensity = self.create_temporal_intensity_features(train_df)
        test_intensity = self.create_temporal_intensity_features(test_df) if test_df is not None else None
        results['intensity'] = (train_intensity, test_intensity)
        
        # 3. Product affinity features
        train_affinity = self.create_product_affinity_features(train_df, is_train=True)
        test_affinity = self.create_product_affinity_features(test_df, is_train=False) if test_df is not None else None
        results['affinity'] = (train_affinity, test_affinity)
        
        # 4. User journey features
        train_journey = self.create_user_journey_features(train_df)
        test_journey = self.create_user_journey_features(test_df) if test_df is not None else None
        results['journey'] = (train_journey, test_journey)
        
        # 5. Value prediction features
        train_value = self.create_value_prediction_features(train_df, is_train=True)
        test_value = self.create_value_prediction_features(test_df, is_train=False) if test_df is not None else None
        results['value'] = (train_value, test_value)
        
        # Combine all features
        train_combined = train_funnel
        for feature_type, (train_feat, _) in results.items():
            if feature_type != 'funnel':
                train_combined = train_combined.merge(train_feat, on='user_session', how='outer')
        
        test_combined = None
        if test_df is not None:
            test_combined = test_funnel
            for feature_type, (_, test_feat) in results.items():
                if feature_type != 'funnel' and test_feat is not None:
                    test_combined = test_combined.merge(test_feat, on='user_session', how='outer')
        
        print(f"\nSpecialized Feature Engineering Complete!")
        print(f"Training sessions: {train_combined.shape}")
        if test_combined is not None:
            print(f"Test sessions: {test_combined.shape}")
        
        return train_combined, test_combined, results

def main():
    """Main function for specialized feature engineering"""
    print("BTK DATATHON 2025 - SPECIALIZED FEATURE ENGINEERING")
    print("=" * 60)
    
    # Load data
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    # Initialize specialized feature engineer
    feature_engineer = SpecializedFeatureEngineer()
    
    # Apply specialized feature engineering
    train_specialized, test_specialized, feature_results = feature_engineer.apply_specialized_feature_engineering(
        train_df, test_df
    )
    
    # Save specialized features
    train_specialized.to_csv('specialized_train_features.csv', index=False)
    test_specialized.to_csv('specialized_test_features.csv', index=False)
    
    print(f"\nSpecialized features saved!")
    print(f"Feature categories created: {list(feature_results.keys())}")
    
    return feature_engineer, train_specialized, test_specialized, feature_results

if __name__ == "__main__":
    feature_engineer, train_specialized, test_specialized, feature_results = main()
