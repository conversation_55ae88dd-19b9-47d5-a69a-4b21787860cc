#!/usr/bin/env python3
"""
Enhanced Model Evaluation with Advanced Feature Engineering
Compare baseline vs enhanced features performance
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class EnhancedModelEvaluator:
    """
    Comprehensive model evaluation with feature engineering comparison
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.results = {}
        
    def load_and_prepare_data(self):
        """Load baseline and enhanced feature datasets"""
        print("Loading baseline and enhanced datasets...")
        
        # Load baseline preprocessed data
        try:
            baseline_train = pd.read_csv('preprocessed_data/train_preprocessed.csv')
            baseline_test = pd.read_csv('preprocessed_data/test_preprocessed.csv')
            print(f"Baseline data loaded: Train {baseline_train.shape}, Test {baseline_test.shape}")
        except FileNotFoundError:
            print("Baseline preprocessed data not found. Running baseline preprocessing...")
            from data_preprocessing import main as preprocess_main
            data = preprocess_main()
            baseline_train = data['X_train'].copy()
            baseline_train['session_value'] = data['y_train']
            baseline_train['user_session'] = data['train_ids']
            baseline_test = data['X_test'].copy()
            baseline_test['user_session'] = data['test_ids']
        
        # Load specialized features
        try:
            specialized_train = pd.read_csv('specialized_train_features.csv')
            specialized_test = pd.read_csv('specialized_test_features.csv')
            print(f"Specialized features loaded: Train {specialized_train.shape}, Test {specialized_test.shape}")
        except FileNotFoundError:
            print("Specialized features not found. Creating them...")
            from feature_engineering_techniques import main as fe_main
            _, specialized_train, specialized_test, _ = fe_main()
        
        return baseline_train, baseline_test, specialized_train, specialized_test
    
    def create_enhanced_dataset(self, baseline_train, baseline_test, specialized_train, specialized_test):
        """Combine baseline and specialized features"""
        print("Creating enhanced dataset...")
        
        # Merge baseline with specialized features
        enhanced_train = baseline_train.merge(specialized_train, on='user_session', how='left')
        enhanced_test = baseline_test.merge(specialized_test, on='user_session', how='left')
        
        # Fill any missing values from merge
        enhanced_train = enhanced_train.fillna(0)
        enhanced_test = enhanced_test.fillna(0)
        
        print(f"Enhanced dataset created: Train {enhanced_train.shape}, Test {enhanced_test.shape}")
        
        return enhanced_train, enhanced_test
    
    def prepare_model_data(self, df, target_col='session_value'):
        """Prepare data for modeling"""
        exclude_cols = [target_col, 'user_session', 'session_value_original']
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        X = df[feature_cols]
        y = df[target_col] if target_col in df.columns else None
        session_ids = df['user_session']
        
        return X, y, session_ids, feature_cols
    
    def evaluate_model_performance(self, X_train, y_train, X_test, y_test, model_name, dataset_name):
        """Evaluate a single model's performance"""
        print(f"Evaluating {model_name} on {dataset_name}...")
        
        # Define models
        models = {
            'Random Forest': RandomForestRegressor(n_estimators=200, random_state=self.random_state, n_jobs=-1),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, random_state=self.random_state),
            'Ridge Regression': Ridge(alpha=1.0, random_state=self.random_state)
        }
        
        results = {}
        
        for name, model in models.items():
            if name != model_name:
                continue
                
            # Train model
            model.fit(X_train, y_train)
            
            # Predictions
            y_train_pred = model.predict(X_train)
            y_test_pred = model.predict(X_test)
            
            # Metrics
            train_mse = mean_squared_error(y_train, y_train_pred)
            test_mse = mean_squared_error(y_test, y_test_pred)
            train_mae = mean_absolute_error(y_train, y_train_pred)
            test_mae = mean_absolute_error(y_test, y_test_pred)
            train_r2 = r2_score(y_train, y_train_pred)
            test_r2 = r2_score(y_test, y_test_pred)
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, 
                                      scoring='neg_mean_squared_error', n_jobs=-1)
            cv_mse = -cv_scores.mean()
            cv_std = cv_scores.std()
            
            results[name] = {
                'train_mse': train_mse,
                'test_mse': test_mse,
                'train_mae': train_mae,
                'test_mae': test_mae,
                'train_r2': train_r2,
                'test_r2': test_r2,
                'cv_mse': cv_mse,
                'cv_std': cv_std,
                'model': model
            }
            
            print(f"  {name} Results:")
            print(f"    Test MSE: {test_mse:.6f}")
            print(f"    Test R²: {test_r2:.6f}")
            print(f"    CV MSE: {cv_mse:.6f} ± {cv_std:.6f}")
        
        return results
    
    def compare_feature_sets(self, baseline_data, enhanced_data):
        """Compare baseline vs enhanced feature performance"""
        print("\n" + "="*60)
        print("FEATURE SET COMPARISON")
        print("="*60)
        
        comparison_results = {}
        
        for dataset_name, data in [('Baseline', baseline_data), ('Enhanced', enhanced_data)]:
            X, y, session_ids, feature_cols = data
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=self.random_state
            )
            
            print(f"\n{dataset_name} Dataset:")
            print(f"  Features: {len(feature_cols)}")
            print(f"  Training samples: {len(X_train)}")
            print(f"  Test samples: {len(X_test)}")
            
            # Evaluate Random Forest (best performing model)
            results = self.evaluate_model_performance(
                X_train, y_train, X_test, y_test, 'Random Forest', dataset_name
            )
            
            comparison_results[dataset_name] = {
                'results': results,
                'feature_count': len(feature_cols),
                'feature_cols': feature_cols,
                'X_train': X_train,
                'y_train': y_train,
                'X_test': X_test,
                'y_test': y_test
            }
        
        return comparison_results
    
    def analyze_feature_importance(self, comparison_results):
        """Analyze and compare feature importance between datasets"""
        print("\n" + "="*60)
        print("FEATURE IMPORTANCE ANALYSIS")
        print("="*60)
        
        importance_analysis = {}
        
        for dataset_name, data in comparison_results.items():
            model = data['results']['Random Forest']['model']
            feature_cols = data['feature_cols']
            
            if hasattr(model, 'feature_importances_'):
                importance_df = pd.DataFrame({
                    'feature': feature_cols,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)
                
                importance_analysis[dataset_name] = importance_df
                
                print(f"\n{dataset_name} - Top 15 Features:")
                print(importance_df.head(15))
        
        return importance_analysis
    
    def create_performance_visualization(self, comparison_results):
        """Create visualizations comparing performance"""
        print("\nCreating performance visualizations...")
        
        # Extract metrics for comparison
        metrics_data = []
        for dataset_name, data in comparison_results.items():
            rf_results = data['results']['Random Forest']
            metrics_data.append({
                'Dataset': dataset_name,
                'Test MSE': rf_results['test_mse'],
                'Test R²': rf_results['test_r2'],
                'CV MSE': rf_results['cv_mse'],
                'Feature Count': data['feature_count']
            })
        
        metrics_df = pd.DataFrame(metrics_data)
        
        # Create comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. MSE Comparison
        metrics_df.plot(x='Dataset', y='Test MSE', kind='bar', ax=axes[0,0], color=['skyblue', 'lightcoral'])
        axes[0,0].set_title('Test MSE Comparison')
        axes[0,0].set_ylabel('MSE')
        axes[0,0].tick_params(axis='x', rotation=0)
        
        # 2. R² Comparison
        metrics_df.plot(x='Dataset', y='Test R²', kind='bar', ax=axes[0,1], color=['lightgreen', 'gold'])
        axes[0,1].set_title('Test R² Comparison')
        axes[0,1].set_ylabel('R²')
        axes[0,1].tick_params(axis='x', rotation=0)
        
        # 3. Feature Count Comparison
        metrics_df.plot(x='Dataset', y='Feature Count', kind='bar', ax=axes[1,0], color=['orange', 'purple'])
        axes[1,0].set_title('Feature Count Comparison')
        axes[1,0].set_ylabel('Number of Features')
        axes[1,0].tick_params(axis='x', rotation=0)
        
        # 4. Performance vs Feature Count
        axes[1,1].scatter(metrics_df['Feature Count'], metrics_df['Test R²'], 
                         c=['blue', 'red'], s=100, alpha=0.7)
        for i, row in metrics_df.iterrows():
            axes[1,1].annotate(row['Dataset'], 
                             (row['Feature Count'], row['Test R²']),
                             xytext=(5, 5), textcoords='offset points')
        axes[1,1].set_xlabel('Feature Count')
        axes[1,1].set_ylabel('Test R²')
        axes[1,1].set_title('Performance vs Feature Count')
        
        plt.tight_layout()
        plt.show()
        
        return metrics_df
    
    def generate_comprehensive_report(self, comparison_results, importance_analysis, metrics_df):
        """Generate comprehensive evaluation report"""
        print("\n" + "="*60)
        print("COMPREHENSIVE EVALUATION REPORT")
        print("="*60)
        
        baseline_results = comparison_results['Baseline']['results']['Random Forest']
        enhanced_results = comparison_results['Enhanced']['results']['Random Forest']
        
        # Performance comparison
        baseline_mse = baseline_results['test_mse']
        enhanced_mse = enhanced_results['test_mse']
        mse_improvement = (baseline_mse - enhanced_mse) / baseline_mse * 100
        
        baseline_r2 = baseline_results['test_r2']
        enhanced_r2 = enhanced_results['test_r2']
        r2_improvement = (enhanced_r2 - baseline_r2) / baseline_r2 * 100
        
        print(f"📊 PERFORMANCE COMPARISON:")
        print(f"Baseline MSE: {baseline_mse:.6f}")
        print(f"Enhanced MSE: {enhanced_mse:.6f}")
        print(f"MSE Improvement: {mse_improvement:.2f}%")
        print(f"")
        print(f"Baseline R²: {baseline_r2:.6f}")
        print(f"Enhanced R²: {enhanced_r2:.6f}")
        print(f"R² Improvement: {r2_improvement:.2f}%")
        
        # Feature analysis
        baseline_features = comparison_results['Baseline']['feature_count']
        enhanced_features = comparison_results['Enhanced']['feature_count']
        feature_increase = enhanced_features - baseline_features
        
        print(f"\n🔧 FEATURE ENGINEERING IMPACT:")
        print(f"Baseline features: {baseline_features}")
        print(f"Enhanced features: {enhanced_features}")
        print(f"New features added: {feature_increase}")
        print(f"Feature increase: {feature_increase/baseline_features*100:.1f}%")
        
        # Top new features
        if 'Enhanced' in importance_analysis and 'Baseline' in importance_analysis:
            enhanced_top = set(importance_analysis['Enhanced'].head(10)['feature'])
            baseline_top = set(importance_analysis['Baseline'].head(10)['feature'])
            new_top_features = enhanced_top - baseline_top
            
            if new_top_features:
                print(f"\n🌟 NEW HIGH-IMPACT FEATURES:")
                for feature in list(new_top_features)[:5]:
                    importance = importance_analysis['Enhanced'][
                        importance_analysis['Enhanced']['feature'] == feature
                    ]['importance'].iloc[0]
                    print(f"  - {feature}: {importance:.4f}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if mse_improvement > 5:
            print("🏆 EXCELLENT: Significant performance improvement!")
        elif mse_improvement > 1:
            print("✅ GOOD: Meaningful performance improvement")
        elif mse_improvement > 0:
            print("📈 MODEST: Small performance improvement")
        else:
            print("⚠️ NO IMPROVEMENT: Enhanced features did not improve performance")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if mse_improvement > 0:
            print("1. Deploy enhanced feature set for production")
            print("2. Consider hyperparameter tuning for further gains")
            print("3. Explore ensemble methods with enhanced features")
        else:
            print("1. Review feature engineering techniques")
            print("2. Consider feature selection to remove noise")
            print("3. Try different model architectures")
        
        print("4. Monitor feature importance for model interpretability")
        print("5. Validate performance on holdout test set")
        
        return {
            'mse_improvement': mse_improvement,
            'r2_improvement': r2_improvement,
            'feature_increase': feature_increase,
            'baseline_mse': baseline_mse,
            'enhanced_mse': enhanced_mse
        }

def main():
    """Main evaluation pipeline"""
    print("BTK DATATHON 2025 - ENHANCED MODEL EVALUATION")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = EnhancedModelEvaluator()
    
    # Load data
    baseline_train, baseline_test, specialized_train, specialized_test = evaluator.load_and_prepare_data()
    
    # Create enhanced dataset
    enhanced_train, enhanced_test = evaluator.create_enhanced_dataset(
        baseline_train, baseline_test, specialized_train, specialized_test
    )
    
    # Prepare data for modeling
    baseline_data = evaluator.prepare_model_data(baseline_train)
    enhanced_data = evaluator.prepare_model_data(enhanced_train)
    
    # Compare feature sets
    comparison_results = evaluator.compare_feature_sets(baseline_data, enhanced_data)
    
    # Analyze feature importance
    importance_analysis = evaluator.analyze_feature_importance(comparison_results)
    
    # Create visualizations
    metrics_df = evaluator.create_performance_visualization(comparison_results)
    
    # Generate comprehensive report
    final_report = evaluator.generate_comprehensive_report(
        comparison_results, importance_analysis, metrics_df
    )
    
    # Save results
    metrics_df.to_csv('feature_engineering_comparison.csv', index=False)
    
    print(f"\nEvaluation complete! Results saved.")
    
    return {
        'evaluator': evaluator,
        'comparison_results': comparison_results,
        'importance_analysis': importance_analysis,
        'metrics_df': metrics_df,
        'final_report': final_report
    }

if __name__ == "__main__":
    results = main()
