#!/usr/bin/env python3
"""
Model Evaluation and Testing for BTK Datathon 2025
MSE-focused evaluation with proper preprocessing validation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

def evaluate_preprocessing_quality(X_train, y_train, X_val, y_val, feature_names):
    """Evaluate the quality of preprocessing"""
    print("\n" + "=" * 60)
    print("PREPROCESSING QUALITY EVALUATION")
    print("=" * 60)
    
    # Check for missing values
    train_missing = X_train.isnull().sum().sum()
    val_missing = X_val.isnull().sum().sum()
    print(f"Missing values - Train: {train_missing}, Validation: {val_missing}")
    
    # Check for infinite values
    train_inf = np.isinf(X_train).sum().sum()
    val_inf = np.isinf(X_val).sum().sum()
    print(f"Infinite values - Train: {train_inf}, Validation: {val_inf}")
    
    # Feature statistics
    print(f"\nFeature Statistics:")
    print(f"Number of features: {len(feature_names)}")
    print(f"Training samples: {len(X_train)}")
    print(f"Validation samples: {len(X_val)}")
    
    # Target distribution
    print(f"\nTarget Distribution (transformed):")
    print(f"Train - Mean: {y_train.mean():.4f}, Std: {y_train.std():.4f}")
    print(f"Val - Mean: {y_val.mean():.4f}, Std: {y_val.std():.4f}")
    
    # Feature scaling check
    print(f"\nFeature Scaling Check (should be ~0 mean, ~1 std for scaled features):")
    train_means = X_train.mean()
    train_stds = X_train.std()
    
    # Check if features are properly scaled
    well_scaled = ((train_means.abs() < 0.1) & (train_stds.between(0.8, 1.2))).sum()
    print(f"Well-scaled features: {well_scaled}/{len(feature_names)} ({well_scaled/len(feature_names)*100:.1f}%)")
    
    return {
        'missing_values': train_missing + val_missing,
        'infinite_values': train_inf + val_inf,
        'well_scaled_features': well_scaled,
        'feature_count': len(feature_names)
    }

def baseline_model_evaluation(X_train, y_train, X_val, y_val, preprocessor):
    """Evaluate baseline models with MSE focus"""
    print("\n" + "=" * 60)
    print("BASELINE MODEL EVALUATION")
    print("=" * 60)
    
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
        'Ridge Regression': Ridge(alpha=1.0, random_state=42)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\nEvaluating {name}...")
        
        # Train model
        model.fit(X_train, y_train)
        
        # Predictions
        y_train_pred = model.predict(X_train)
        y_val_pred = model.predict(X_val)
        
        # Metrics on transformed scale
        train_mse = mean_squared_error(y_train, y_train_pred)
        val_mse = mean_squared_error(y_val, y_val_pred)
        train_mae = mean_absolute_error(y_train, y_train_pred)
        val_mae = mean_absolute_error(y_val, y_val_pred)
        train_r2 = r2_score(y_train, y_train_pred)
        val_r2 = r2_score(y_val, y_val_pred)
        
        # Transform back to original scale for interpretation
        y_train_orig = preprocessor.inverse_transform_target(y_train)
        y_val_orig = preprocessor.inverse_transform_target(y_val)
        y_train_pred_orig = preprocessor.inverse_transform_target(y_train_pred)
        y_val_pred_orig = preprocessor.inverse_transform_target(y_val_pred)
        
        # Metrics on original scale
        train_mse_orig = mean_squared_error(y_train_orig, y_train_pred_orig)
        val_mse_orig = mean_squared_error(y_val_orig, y_val_pred_orig)
        train_mae_orig = mean_absolute_error(y_train_orig, y_train_pred_orig)
        val_mae_orig = mean_absolute_error(y_val_orig, y_val_pred_orig)
        
        results[name] = {
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_r2': train_r2,
            'val_r2': val_r2,
            'train_mse_orig': train_mse_orig,
            'val_mse_orig': val_mse_orig,
            'train_mae_orig': train_mae_orig,
            'val_mae_orig': val_mae_orig,
            'model': model
        }
        
        print(f"Transformed Scale:")
        print(f"  Train MSE: {train_mse:.4f}, Val MSE: {val_mse:.4f}")
        print(f"  Train MAE: {train_mae:.4f}, Val MAE: {val_mae:.4f}")
        print(f"  Train R²: {train_r2:.4f}, Val R²: {val_r2:.4f}")
        
        print(f"Original Scale:")
        print(f"  Train MSE: {train_mse_orig:.2f}, Val MSE: {val_mse_orig:.2f}")
        print(f"  Train MAE: {train_mae_orig:.2f}, Val MAE: {val_mae_orig:.2f}")
        
        # Overfitting check
        mse_diff = val_mse - train_mse
        if mse_diff > 0.1:
            print(f"  ⚠️  Potential overfitting detected (MSE diff: {mse_diff:.4f})")
        else:
            print(f"  ✅ Good generalization (MSE diff: {mse_diff:.4f})")
    
    return results

def feature_importance_analysis(model_results, feature_names):
    """Analyze feature importance from trained models"""
    print("\n" + "=" * 60)
    print("FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)
    
    # Get Random Forest feature importance
    rf_model = model_results['Random Forest']['model']
    
    if hasattr(rf_model, 'feature_importances_'):
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': rf_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("Top 15 Most Important Features:")
        print(importance_df.head(15))
        
        # Visualize feature importance
        plt.figure(figsize=(12, 8))
        top_features = importance_df.head(20)
        plt.barh(range(len(top_features)), top_features['importance'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('Feature Importance')
        plt.title('Top 20 Feature Importance (Random Forest)')
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.show()
        
        return importance_df
    
    return None

def prediction_analysis(y_true, y_pred, y_true_orig, y_pred_orig, model_name):
    """Analyze prediction quality"""
    print(f"\n{model_name} - Prediction Analysis:")
    
    # Residuals analysis
    residuals = y_true - y_pred
    residuals_orig = y_true_orig - y_pred_orig
    
    print(f"Residuals Statistics (transformed):")
    print(f"  Mean: {residuals.mean():.4f}")
    print(f"  Std: {residuals.std():.4f}")
    print(f"  Min: {residuals.min():.4f}")
    print(f"  Max: {residuals.max():.4f}")
    
    print(f"Residuals Statistics (original):")
    print(f"  Mean: {residuals_orig.mean():.2f}")
    print(f"  Std: {residuals_orig.std():.2f}")
    print(f"  Min: {residuals_orig.min():.2f}")
    print(f"  Max: {residuals_orig.max():.2f}")
    
    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Actual vs Predicted (transformed)
    axes[0,0].scatter(y_true, y_pred, alpha=0.5, s=10)
    axes[0,0].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
    axes[0,0].set_xlabel('Actual (transformed)')
    axes[0,0].set_ylabel('Predicted (transformed)')
    axes[0,0].set_title(f'{model_name} - Actual vs Predicted (Transformed)')
    
    # 2. Actual vs Predicted (original)
    axes[0,1].scatter(y_true_orig, y_pred_orig, alpha=0.5, s=10)
    axes[0,1].plot([y_true_orig.min(), y_true_orig.max()], [y_true_orig.min(), y_true_orig.max()], 'r--', lw=2)
    axes[0,1].set_xlabel('Actual Session Value')
    axes[0,1].set_ylabel('Predicted Session Value')
    axes[0,1].set_title(f'{model_name} - Actual vs Predicted (Original Scale)')
    
    # 3. Residuals plot (transformed)
    axes[1,0].scatter(y_pred, residuals, alpha=0.5, s=10)
    axes[1,0].axhline(y=0, color='r', linestyle='--')
    axes[1,0].set_xlabel('Predicted (transformed)')
    axes[1,0].set_ylabel('Residuals')
    axes[1,0].set_title(f'{model_name} - Residuals Plot (Transformed)')
    
    # 4. Residuals histogram
    axes[1,1].hist(residuals_orig, bins=50, alpha=0.7)
    axes[1,1].set_xlabel('Residuals (Original Scale)')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].set_title(f'{model_name} - Residuals Distribution')
    
    plt.tight_layout()
    plt.show()

def cross_validation_evaluation(X_train, y_train, preprocessor):
    """Perform cross-validation evaluation"""
    print("\n" + "=" * 60)
    print("CROSS-VALIDATION EVALUATION")
    print("=" * 60)
    
    # Use Random Forest for CV
    rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    
    # Perform 5-fold CV
    cv_scores = cross_val_score(rf_model, X_train, y_train, cv=5, 
                               scoring='neg_mean_squared_error', n_jobs=-1)
    
    cv_mse_scores = -cv_scores
    
    print(f"5-Fold Cross-Validation Results (MSE on transformed scale):")
    print(f"  Mean MSE: {cv_mse_scores.mean():.4f}")
    print(f"  Std MSE: {cv_mse_scores.std():.4f}")
    print(f"  Min MSE: {cv_mse_scores.min():.4f}")
    print(f"  Max MSE: {cv_mse_scores.max():.4f}")
    
    # Estimate original scale MSE (approximate)
    print(f"\nEstimated Original Scale Performance:")
    print(f"  Expected MSE range: {cv_mse_scores.mean()*400:.0f} ± {cv_mse_scores.std()*400:.0f}")
    
    return cv_mse_scores

def generate_performance_report(preprocessing_quality, model_results, cv_scores):
    """Generate comprehensive performance report"""
    print("\n" + "=" * 60)
    print("COMPREHENSIVE PERFORMANCE REPORT")
    print("=" * 60)
    
    print("📊 PREPROCESSING QUALITY:")
    print(f"✅ Data Quality Score: {100 - (preprocessing_quality['missing_values'] + preprocessing_quality['infinite_values'])}/100")
    print(f"✅ Feature Engineering: {preprocessing_quality['feature_count']} features created")
    print(f"✅ Scaling Quality: {preprocessing_quality['well_scaled_features']}/{preprocessing_quality['feature_count']} features well-scaled")
    
    print("\n🎯 MODEL PERFORMANCE (MSE - Lower is Better):")
    best_model = min(model_results.keys(), key=lambda x: model_results[x]['val_mse'])
    
    for name, results in model_results.items():
        status = "🏆" if name == best_model else "📈"
        print(f"{status} {name}:")
        print(f"    Validation MSE (transformed): {results['val_mse']:.4f}")
        print(f"    Validation MSE (original): {results['val_mse_orig']:.2f}")
        print(f"    Validation R²: {results['val_r2']:.4f}")
        
        # Overfitting check
        overfitting = results['val_mse'] - results['train_mse']
        if overfitting > 0.1:
            print(f"    ⚠️  Overfitting: {overfitting:.4f}")
        else:
            print(f"    ✅ Generalization: {overfitting:.4f}")
    
    print(f"\n🔄 CROSS-VALIDATION:")
    print(f"    CV MSE (transformed): {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    print(f"    Stability: {'Good' if cv_scores.std() < 0.1 else 'Needs improvement'}")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    best_val_mse = model_results[best_model]['val_mse_orig']
    
    if best_val_mse < 300:
        print("🏆 EXCELLENT: Model performance exceeds expectations!")
    elif best_val_mse < 500:
        print("✅ GOOD: Model performance meets expectations")
    elif best_val_mse < 800:
        print("📈 FAIR: Model needs improvement - try feature engineering")
    else:
        print("⚠️  POOR: Model needs significant improvement")
    
    print(f"\n💡 NEXT STEPS:")
    print("1. Try advanced models (XGBoost, LightGBM)")
    print("2. Hyperparameter tuning")
    print("3. Feature selection and engineering")
    print("4. Ensemble methods")
    print("5. Advanced validation strategies")

def main():
    """Main evaluation pipeline"""
    print("BTK DATATHON 2025 - MODEL EVALUATION")
    print("=" * 60)
    
    # This would typically load preprocessed data
    # For demo, we'll import from preprocessing
    from data_preprocessing import main as preprocess_main
    
    # Get preprocessed data
    data = preprocess_main()
    
    # Extract data
    X_train = data['X_train']
    y_train = data['y_train']
    X_val = data['X_val']
    y_val = data['y_val']
    preprocessor = data['preprocessor']
    
    # Evaluate preprocessing quality
    preprocessing_quality = evaluate_preprocessing_quality(
        X_train, y_train, X_val, y_val, preprocessor.feature_columns
    )
    
    # Evaluate baseline models
    model_results = baseline_model_evaluation(X_train, y_train, X_val, y_val, preprocessor)
    
    # Feature importance analysis
    importance_df = feature_importance_analysis(model_results, preprocessor.feature_columns)
    
    # Detailed prediction analysis for best model
    best_model_name = min(model_results.keys(), key=lambda x: model_results[x]['val_mse'])
    best_model = model_results[best_model_name]['model']
    
    y_val_pred = best_model.predict(X_val)
    y_val_orig = preprocessor.inverse_transform_target(y_val)
    y_val_pred_orig = preprocessor.inverse_transform_target(y_val_pred)
    
    prediction_analysis(y_val, y_val_pred, y_val_orig, y_val_pred_orig, best_model_name)
    
    # Cross-validation
    cv_scores = cross_validation_evaluation(X_train, y_train, preprocessor)
    
    # Generate final report
    generate_performance_report(preprocessing_quality, model_results, cv_scores)
    
    return {
        'preprocessing_quality': preprocessing_quality,
        'model_results': model_results,
        'feature_importance': importance_df,
        'cv_scores': cv_scores,
        'best_model': best_model_name
    }

if __name__ == "__main__":
    evaluation_results = main()
