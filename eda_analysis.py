#!/usr/bin/env python3
"""
Exploratory Data Analysis for BTK Datathon 2025
E-commerce Session Value Prediction Dataset
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_data():
    """Load the training and test datasets"""
    print("Loading datasets...")
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_submission = pd.read_csv('sample_submission.csv')
    
    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")
    print(f"Sample submission shape: {sample_submission.shape}")
    
    return train_df, test_df, sample_submission

def basic_info_analysis(df, dataset_name):
    """Analyze basic information about the dataset"""
    print(f"\n{'='*50}")
    print(f"BASIC INFO ANALYSIS - {dataset_name}")
    print(f"{'='*50}")
    
    print(f"\nDataset Shape: {df.shape}")
    print(f"Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
    print("\nColumn Information:")
    print(df.info())
    
    print("\nData Types:")
    print(df.dtypes)
    
    print("\nFirst 5 rows:")
    print(df.head())
    
    return df.info()

def missing_values_analysis(df, dataset_name):
    """Analyze missing values in the dataset"""
    print(f"\n{'='*50}")
    print(f"MISSING VALUES ANALYSIS - {dataset_name}")
    print(f"{'='*50}")
    
    missing_data = df.isnull().sum()
    missing_percent = (missing_data / len(df)) * 100
    
    missing_df = pd.DataFrame({
        'Column': missing_data.index,
        'Missing Count': missing_data.values,
        'Missing Percentage': missing_percent.values
    })
    
    missing_df = missing_df[missing_df['Missing Count'] > 0].sort_values('Missing Count', ascending=False)
    
    if len(missing_df) > 0:
        print("Missing Values Summary:")
        print(missing_df)
        
        # Visualize missing values
        plt.figure(figsize=(10, 6))
        sns.heatmap(df.isnull(), cbar=True, yticklabels=False, cmap='viridis')
        plt.title(f'Missing Values Heatmap - {dataset_name}')
        plt.tight_layout()
        plt.show()
    else:
        print("No missing values found in the dataset!")
    
    return missing_df

def statistical_analysis(df, dataset_name):
    """Perform statistical analysis on numerical columns"""
    print(f"\n{'='*50}")
    print(f"STATISTICAL ANALYSIS - {dataset_name}")
    print(f"{'='*50}")
    
    # Identify numerical columns
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    if numerical_cols:
        print(f"\nNumerical columns: {numerical_cols}")
        print("\nDescriptive Statistics:")
        print(df[numerical_cols].describe())
        
        # Distribution plots for numerical columns
        if len(numerical_cols) > 0:
            fig, axes = plt.subplots(1, len(numerical_cols), figsize=(6*len(numerical_cols), 5))
            if len(numerical_cols) == 1:
                axes = [axes]
            
            for i, col in enumerate(numerical_cols):
                df[col].hist(bins=50, ax=axes[i], alpha=0.7)
                axes[i].set_title(f'Distribution of {col}')
                axes[i].set_xlabel(col)
                axes[i].set_ylabel('Frequency')
            
            plt.tight_layout()
            plt.show()
    else:
        print("No numerical columns found for statistical analysis.")

def categorical_analysis(df, dataset_name):
    """Analyze categorical columns"""
    print(f"\n{'='*50}")
    print(f"CATEGORICAL ANALYSIS - {dataset_name}")
    print(f"{'='*50}")
    
    # Identify categorical columns
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    
    # Remove datetime columns if any
    datetime_cols = []
    for col in categorical_cols:
        if 'time' in col.lower() or 'date' in col.lower():
            datetime_cols.append(col)
    
    categorical_cols = [col for col in categorical_cols if col not in datetime_cols]
    
    print(f"\nCategorical columns: {categorical_cols}")
    print(f"Datetime columns: {datetime_cols}")
    
    for col in categorical_cols:
        print(f"\n{col} - Unique Values: {df[col].nunique()}")
        value_counts = df[col].value_counts()
        print(f"Top 10 values:")
        print(value_counts.head(10))
        
        # Visualize top categories
        plt.figure(figsize=(12, 6))
        top_10 = value_counts.head(10)
        top_10.plot(kind='bar')
        plt.title(f'Top 10 {col} Distribution - {dataset_name}')
        plt.xlabel(col)
        plt.ylabel('Count')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

def datetime_analysis(df, dataset_name):
    """Analyze datetime columns"""
    print(f"\n{'='*50}")
    print(f"DATETIME ANALYSIS - {dataset_name}")
    print(f"{'='*50}")
    
    datetime_cols = [col for col in df.columns if 'time' in col.lower() or 'date' in col.lower()]
    
    for col in datetime_cols:
        if col in df.columns:
            print(f"\nAnalyzing {col}:")
            
            # Convert to datetime
            df[col] = pd.to_datetime(df[col])
            
            print(f"Date range: {df[col].min()} to {df[col].max()}")
            print(f"Total days: {(df[col].max() - df[col].min()).days}")
            
            # Extract time components
            df[f'{col}_hour'] = df[col].dt.hour
            df[f'{col}_day_of_week'] = df[col].dt.dayofweek
            df[f'{col}_day'] = df[col].dt.day
            
            # Visualize temporal patterns
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Hourly distribution
            df[f'{col}_hour'].value_counts().sort_index().plot(kind='bar', ax=axes[0,0])
            axes[0,0].set_title(f'Hourly Distribution - {col}')
            axes[0,0].set_xlabel('Hour of Day')
            
            # Day of week distribution
            day_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            day_counts = df[f'{col}_day_of_week'].value_counts().sort_index()
            day_counts.index = [day_names[i] for i in day_counts.index]
            day_counts.plot(kind='bar', ax=axes[0,1])
            axes[0,1].set_title(f'Day of Week Distribution - {col}')
            
            # Daily trend
            daily_counts = df[col].dt.date.value_counts().sort_index()
            daily_counts.plot(ax=axes[1,0])
            axes[1,0].set_title(f'Daily Trend - {col}')
            axes[1,0].tick_params(axis='x', rotation=45)
            
            # Time series plot
            df[col].hist(bins=50, ax=axes[1,1])
            axes[1,1].set_title(f'Timestamp Distribution - {col}')
            
            plt.tight_layout()
            plt.show()

def target_variable_analysis(df, target_col='session_value'):
    """Analyze the target variable in detail"""
    print(f"\n{'='*50}")
    print(f"TARGET VARIABLE ANALYSIS - {target_col}")
    print(f"{'='*50}")

    if target_col not in df.columns:
        print(f"Target column '{target_col}' not found in dataset!")
        return

    target = df[target_col]

    print(f"\nTarget Variable: {target_col}")
    print(f"Data Type: {target.dtype}")
    print(f"Non-null Count: {target.count()}")
    print(f"Null Count: {target.isnull().sum()}")

    print(f"\nDescriptive Statistics:")
    print(target.describe())

    print(f"\nTarget Distribution:")
    print(f"Min: {target.min()}")
    print(f"Max: {target.max()}")
    print(f"Mean: {target.mean():.4f}")
    print(f"Median: {target.median():.4f}")
    print(f"Std: {target.std():.4f}")
    print(f"Skewness: {target.skew():.4f}")
    print(f"Kurtosis: {target.kurtosis():.4f}")

    # Check for class imbalance (if target appears to be categorical)
    unique_values = target.nunique()
    print(f"\nUnique Values: {unique_values}")

    if unique_values <= 20:  # Likely categorical
        print(f"\nValue Counts:")
        value_counts = target.value_counts()
        print(value_counts)

        print(f"\nClass Distribution (%):")
        print((value_counts / len(target) * 100).round(2))

        # Check for class imbalance
        max_class_pct = (value_counts.max() / len(target)) * 100
        min_class_pct = (value_counts.min() / len(target)) * 100
        imbalance_ratio = value_counts.max() / value_counts.min()

        print(f"\nClass Imbalance Analysis:")
        print(f"Majority class: {max_class_pct:.2f}%")
        print(f"Minority class: {min_class_pct:.2f}%")
        print(f"Imbalance ratio: {imbalance_ratio:.2f}:1")

        if imbalance_ratio > 10:
            print("⚠️  SEVERE CLASS IMBALANCE DETECTED!")
        elif imbalance_ratio > 3:
            print("⚠️  Moderate class imbalance detected")
        else:
            print("✅ Classes are relatively balanced")

    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Histogram
    target.hist(bins=50, ax=axes[0,0], alpha=0.7)
    axes[0,0].set_title(f'Distribution of {target_col}')
    axes[0,0].set_xlabel(target_col)
    axes[0,0].set_ylabel('Frequency')

    # Box plot
    target.plot(kind='box', ax=axes[0,1])
    axes[0,1].set_title(f'Box Plot of {target_col}')
    axes[0,1].set_ylabel(target_col)

    # Q-Q plot for normality check
    from scipy import stats
    stats.probplot(target.dropna(), dist="norm", plot=axes[1,0])
    axes[1,0].set_title(f'Q-Q Plot - {target_col}')

    # Log transformation if all values are positive
    if target.min() > 0:
        log_target = np.log1p(target)
        log_target.hist(bins=50, ax=axes[1,1], alpha=0.7)
        axes[1,1].set_title(f'Log-transformed {target_col}')
        axes[1,1].set_xlabel(f'log({target_col})')
        axes[1,1].set_ylabel('Frequency')
    else:
        # Density plot
        target.plot(kind='density', ax=axes[1,1])
        axes[1,1].set_title(f'Density Plot - {target_col}')
        axes[1,1].set_xlabel(target_col)

    plt.tight_layout()
    plt.show()

def correlation_analysis(df, dataset_name):
    """Analyze correlations between variables"""
    print(f"\n{'='*50}")
    print(f"CORRELATION ANALYSIS - {dataset_name}")
    print(f"{'='*50}")

    # Get numerical columns
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()

    if len(numerical_cols) < 2:
        print("Not enough numerical columns for correlation analysis.")
        return

    # Calculate correlation matrix
    corr_matrix = df[numerical_cols].corr()

    print("Correlation Matrix:")
    print(corr_matrix)

    # Visualize correlation matrix
    plt.figure(figsize=(10, 8))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    plt.title(f'Correlation Matrix - {dataset_name}')
    plt.tight_layout()
    plt.show()

    # Find high correlations
    high_corr_pairs = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.7:  # High correlation threshold
                high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_val))

    if high_corr_pairs:
        print(f"\nHigh Correlations (|r| > 0.7):")
        for col1, col2, corr_val in high_corr_pairs:
            print(f"{col1} <-> {col2}: {corr_val:.4f}")
    else:
        print("\nNo high correlations found (|r| > 0.7)")

def feature_engineering_insights(df, dataset_name):
    """Provide insights for potential feature engineering"""
    print(f"\n{'='*50}")
    print(f"FEATURE ENGINEERING INSIGHTS - {dataset_name}")
    print(f"{'='*50}")

    print("\n🔍 POTENTIAL FEATURE ENGINEERING OPPORTUNITIES:")

    # Check for ID columns that might need encoding
    id_cols = [col for col in df.columns if 'id' in col.lower()]
    if id_cols:
        print(f"\n📋 ID Columns found: {id_cols}")
        for col in id_cols:
            unique_count = df[col].nunique()
            total_count = len(df)
            print(f"  - {col}: {unique_count} unique values ({unique_count/total_count*100:.1f}% unique)")
            if unique_count/total_count > 0.8:
                print(f"    💡 Consider target encoding or frequency encoding for {col}")

    # Check for session-based features
    session_cols = [col for col in df.columns if 'session' in col.lower()]
    if session_cols:
        print(f"\n🔄 Session Columns: {session_cols}")
        print("    💡 Consider aggregating features by session (count, sum, mean, etc.)")

    # Check for categorical columns with high cardinality
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    datetime_cols = [col for col in categorical_cols if 'time' in col.lower()]
    categorical_cols = [col for col in categorical_cols if col not in datetime_cols]

    print(f"\n📊 Categorical Columns Analysis:")
    for col in categorical_cols:
        unique_count = df[col].nunique()
        total_count = len(df)
        print(f"  - {col}: {unique_count} unique values")
        if unique_count > 100:
            print(f"    ⚠️  High cardinality - consider grouping or encoding strategies")
        elif unique_count < 10:
            print(f"    ✅ Good for one-hot encoding")

    # Datetime feature suggestions
    if datetime_cols:
        print(f"\n📅 Datetime Features: {datetime_cols}")
        print("    💡 Consider extracting: hour, day_of_week, month, is_weekend, time_since_last_event")

def main():
    """Main function to run the complete EDA"""
    print("BTK DATATHON 2025 - EXPLORATORY DATA ANALYSIS")
    print("=" * 60)

    # Load data
    train_df, test_df, sample_submission = load_data()

    # Analyze training data
    basic_info_analysis(train_df, "TRAINING DATA")
    missing_values_analysis(train_df, "TRAINING DATA")
    statistical_analysis(train_df, "TRAINING DATA")
    target_variable_analysis(train_df, "session_value")
    categorical_analysis(train_df, "TRAINING DATA")
    datetime_analysis(train_df, "TRAINING DATA")
    correlation_analysis(train_df, "TRAINING DATA")
    feature_engineering_insights(train_df, "TRAINING DATA")

    # Analyze test data
    print("\n" + "="*60)
    print("ANALYZING TEST DATA")
    print("="*60)
    basic_info_analysis(test_df, "TEST DATA")
    missing_values_analysis(test_df, "TEST DATA")
    statistical_analysis(test_df, "TEST DATA")
    categorical_analysis(test_df, "TEST DATA")
    datetime_analysis(test_df, "TEST DATA")
    correlation_analysis(test_df, "TEST DATA")
    feature_engineering_insights(test_df, "TEST DATA")

    # Data consistency check
    print(f"\n{'='*50}")
    print("DATA CONSISTENCY CHECK")
    print(f"{'='*50}")

    common_cols = set(train_df.columns) & set(test_df.columns)
    train_only = set(train_df.columns) - set(test_df.columns)
    test_only = set(test_df.columns) - set(train_df.columns)

    print(f"Common columns: {len(common_cols)}")
    print(f"Train-only columns: {train_only}")
    print(f"Test-only columns: {test_only}")

    # Check data types consistency
    for col in common_cols:
        if train_df[col].dtype != test_df[col].dtype:
            print(f"⚠️  Data type mismatch in {col}: train={train_df[col].dtype}, test={test_df[col].dtype}")

    print("\n" + "="*60)
    print("EDA COMPLETE!")
    print("="*60)

if __name__ == "__main__":
    main()
