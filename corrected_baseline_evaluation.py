#!/usr/bin/env python3
"""
Corrected Baseline Model Evaluation with Proper Inverse Transformation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression, <PERSON>, <PERSON><PERSON>
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_simple_data():
    """Load data and create simple baseline features"""
    print("Loading and preparing data for baseline models...")
    
    # Load original data
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    # Convert datetime
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    test_df['event_time'] = pd.to_datetime(test_df['event_time'])
    
    # Create simple session-level features
    def create_simple_session_features(df, is_train=True):
        session_features = df.groupby('user_session').agg({
            'event_time': 'count',  # Total events
            'product_id': 'nunique',  # Unique products
            'category_id': 'nunique',  # Unique categories
            'user_id': 'first'  # User ID
        }).rename(columns={
            'event_time': 'total_events',
            'product_id': 'unique_products', 
            'category_id': 'unique_categories'
        }).reset_index()
        
        # Event type counts
        event_counts = df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
        event_counts = event_counts.reset_index()
        
        # Merge
        session_features = session_features.merge(event_counts, on='user_session', how='left')
        
        # Fill missing event types
        for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
            if event_type not in session_features.columns:
                session_features[event_type] = 0
            session_features[event_type] = session_features[event_type].fillna(0)
        
        # Simple ratios
        session_features['buy_ratio'] = session_features['BUY'] / (session_features['total_events'] + 1e-6)
        session_features['cart_ratio'] = session_features['ADD_CART'] / (session_features['total_events'] + 1e-6)
        
        # Add target for training
        if is_train:
            target_values = df.groupby('user_session')['session_value'].first()
            session_features = session_features.merge(target_values.reset_index(), on='user_session', how='left')
        
        return session_features
    
    # Create session features
    train_sessions = create_simple_session_features(train_df, is_train=True)
    test_sessions = create_simple_session_features(test_df, is_train=False)
    
    print(f"Session features created:")
    print(f"  Training: {train_sessions.shape}")
    print(f"  Test: {test_sessions.shape}")
    
    return train_sessions, test_sessions

def evaluate_baseline_models(train_sessions, test_sessions):
    """Evaluate simple baseline models"""
    print("\n" + "="*60)
    print("BASELINE MODEL EVALUATION")
    print("="*60)
    
    # Prepare features
    feature_cols = ['total_events', 'unique_products', 'unique_categories', 
                   'VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY', 'buy_ratio', 'cart_ratio']
    
    X = train_sessions[feature_cols]
    y = train_sessions['session_value']
    
    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Initialize simple models
    models = {
        'Linear Regression': LinearRegression(),
        'Ridge Regression': Ridge(alpha=1.0, random_state=42),
        'Lasso Regression': Lasso(alpha=0.1, random_state=42),
        'Decision Tree': DecisionTreeRegressor(max_depth=10, random_state=42),
        'Random Forest': RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42)
    }
    
    results = {}
    
    print("Model Performance (MSE - Competition Metric):")
    print("=" * 70)
    
    for name, model in models.items():
        print(f"\nEvaluating {name}...")
        
        # Train model
        model.fit(X_train, y_train)
        
        # Predictions
        y_train_pred = model.predict(X_train)
        y_val_pred = model.predict(X_val)
        
        # Calculate MSE (competition metric)
        train_mse = mean_squared_error(y_train, y_train_pred)
        val_mse = mean_squared_error(y_val, y_val_pred)
        
        # Additional metrics for analysis
        train_mae = mean_absolute_error(y_train, y_train_pred)
        val_mae = mean_absolute_error(y_val, y_val_pred)
        train_r2 = r2_score(y_train, y_train_pred)
        val_r2 = r2_score(y_val, y_val_pred)
        
        # Cross-validation MSE
        cv_scores = cross_val_score(model, X, y, cv=5, scoring='neg_mean_squared_error')
        cv_mse = -cv_scores.mean()
        cv_std = cv_scores.std()
        
        # Store results
        results[name] = {
            'model': model,
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_r2': train_r2,
            'val_r2': val_r2,
            'cv_mse': cv_mse,
            'cv_std': cv_std,
            'overfitting': val_mse - train_mse
        }
        
        # Print results
        print(f"  🎯 Validation MSE: {val_mse:.2f}")
        print(f"  📊 Validation R²: {val_r2:.4f}")
        print(f"  🔄 CV MSE: {cv_mse:.2f} ± {cv_std:.2f}")
        print(f"  📈 Overfitting: {val_mse - train_mse:.2f}")
    
    return results, X_train, X_val, y_train, y_val, feature_cols

def create_performance_analysis(results):
    """Create comprehensive performance analysis"""
    print("\n" + "="*60)
    print("PERFORMANCE ANALYSIS")
    print("="*60)
    
    # Create comparison DataFrame
    comparison_data = []
    for name, result in results.items():
        comparison_data.append({
            'Model': name,
            'Validation MSE': result['val_mse'],
            'Validation R²': result['val_r2'],
            'CV MSE': result['cv_mse'],
            'CV Std': result['cv_std'],
            'Overfitting': result['overfitting']
        })
    
    comparison_df = pd.DataFrame(comparison_data)
    comparison_df = comparison_df.sort_values('Validation MSE')
    
    print("🏆 MODEL RANKING (by Validation MSE):")
    print("=" * 70)
    for i, row in comparison_df.iterrows():
        rank = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
        print(f"{rank} {row['Model']:<20} | MSE: {row['Validation MSE']:>8.2f} | R²: {row['Validation R²']:>7.4f}")
    
    # Best model analysis
    best_model_name = comparison_df.iloc[0]['Model']
    best_result = results[best_model_name]
    
    print(f"\n🎯 BEST MODEL ANALYSIS: {best_model_name}")
    print(f"  Competition Metric (MSE): {best_result['val_mse']:.2f}")
    print(f"  Explained Variance (R²): {best_result['val_r2']:.4f}")
    print(f"  Cross-validation Stability: {best_result['cv_std']:.4f}")
    
    # Performance assessment
    if best_result['val_r2'] > 0.9:
        assessment = "🏆 EXCELLENT"
    elif best_result['val_r2'] > 0.8:
        assessment = "✅ GOOD"
    elif best_result['val_r2'] > 0.6:
        assessment = "📈 FAIR"
    else:
        assessment = "⚠️ POOR"
    
    print(f"  Performance Assessment: {assessment}")
    
    return comparison_df, best_model_name

def generate_predictions(best_model_name, results, test_sessions, feature_cols):
    """Generate predictions for test set"""
    print(f"\n🔮 Generating predictions using {best_model_name}...")
    
    best_model = results[best_model_name]['model']
    
    # Prepare test features
    X_test = test_sessions[feature_cols]
    
    # Generate predictions
    test_predictions = best_model.predict(X_test)
    
    # Create submission
    submission_df = pd.DataFrame({
        'user_session': test_sessions['user_session'],
        'session_value': test_predictions
    })
    
    # Save submission
    submission_df.to_csv('corrected_baseline_submission.csv', index=False)
    
    print(f"✅ Predictions saved to corrected_baseline_submission.csv")
    print(f"📊 Prediction Statistics:")
    print(f"  Count: {len(submission_df):,}")
    print(f"  Min: {submission_df['session_value'].min():.2f}")
    print(f"  Max: {submission_df['session_value'].max():.2f}")
    print(f"  Mean: {submission_df['session_value'].mean():.2f}")
    print(f"  Median: {submission_df['session_value'].median():.2f}")
    print(f"  Std: {submission_df['session_value'].std():.2f}")
    
    return submission_df

def create_visualizations(results, X_val, y_val, best_model_name):
    """Create performance visualizations"""
    print("\nCreating performance visualizations...")
    
    # Performance comparison plot
    comparison_data = [(name, result['val_mse'], result['val_r2']) 
                      for name, result in results.items()]
    comparison_data.sort(key=lambda x: x[1])  # Sort by MSE
    
    models, mse_scores, r2_scores = zip(*comparison_data)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. MSE Comparison
    axes[0,0].bar(range(len(models)), mse_scores, color='skyblue')
    axes[0,0].set_xticks(range(len(models)))
    axes[0,0].set_xticklabels(models, rotation=45, ha='right')
    axes[0,0].set_title('Validation MSE Comparison (Competition Metric)')
    axes[0,0].set_ylabel('MSE')
    
    # 2. R² Comparison
    axes[0,1].bar(range(len(models)), r2_scores, color='lightgreen')
    axes[0,1].set_xticks(range(len(models)))
    axes[0,1].set_xticklabels(models, rotation=45, ha='right')
    axes[0,1].set_title('Validation R² Comparison')
    axes[0,1].set_ylabel('R²')
    
    # 3. Actual vs Predicted for best model
    best_model = results[best_model_name]['model']
    y_val_pred = best_model.predict(X_val)
    
    axes[1,0].scatter(y_val, y_val_pred, alpha=0.5, s=10)
    axes[1,0].plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 'r--', lw=2)
    axes[1,0].set_xlabel('Actual Session Value')
    axes[1,0].set_ylabel('Predicted Session Value')
    axes[1,0].set_title(f'Actual vs Predicted - {best_model_name}')
    
    # 4. Residuals plot
    residuals = y_val - y_val_pred
    axes[1,1].scatter(y_val_pred, residuals, alpha=0.5, s=10)
    axes[1,1].axhline(y=0, color='r', linestyle='--')
    axes[1,1].set_xlabel('Predicted Session Value')
    axes[1,1].set_ylabel('Residuals')
    axes[1,1].set_title(f'Residuals Plot - {best_model_name}')
    
    plt.tight_layout()
    plt.show()

def analyze_feature_importance(best_model, feature_cols):
    """Analyze feature importance for tree-based models"""
    print("\n📊 FEATURE IMPORTANCE ANALYSIS")
    print("="*50)
    
    if hasattr(best_model, 'feature_importances_'):
        importance_df = pd.DataFrame({
            'Feature': feature_cols,
            'Importance': best_model.feature_importances_
        }).sort_values('Importance', ascending=False)
        
        print("Top 10 Most Important Features:")
        for i, row in importance_df.head(10).iterrows():
            print(f"  {row.name + 1:2d}. {row['Feature']:<25} | {row['Importance']:.4f}")
        
        # Visualize feature importance
        plt.figure(figsize=(12, 8))
        top_features = importance_df.head(15)
        plt.barh(range(len(top_features)), top_features['Importance'])
        plt.yticks(range(len(top_features)), top_features['Feature'])
        plt.xlabel('Feature Importance')
        plt.title('Feature Importance - Random Forest')
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.show()
        
        return importance_df
    else:
        print("Feature importance not available for this model type")
        return None

def generate_final_report(results, comparison_df, best_model_name, submission_stats):
    """Generate comprehensive final report"""
    print("\n" + "="*60)
    print("COMPREHENSIVE BASELINE MODEL REPORT")
    print("="*60)
    
    best_result = results[best_model_name]
    
    print(f"🏆 COMPETITION RESULTS:")
    print(f"  Best Model: {best_model_name}")
    print(f"  Competition Metric (MSE): {best_result['val_mse']:.2f}")
    print(f"  Model Performance (R²): {best_result['val_r2']:.4f}")
    print(f"  Cross-validation MSE: {best_result['cv_mse']:.2f} ± {best_result['cv_std']:.2f}")
    
    print(f"\n📊 MODEL COMPARISON:")
    for i, row in comparison_df.head(3).iterrows():
        status = "🥇" if i == 0 else "🥈" if i == 1 else "🥉"
        print(f"  {status} {row['Model']}: MSE = {row['Validation MSE']:.2f}")
    
    print(f"\n🔍 MODEL ANALYSIS:")
    if best_result['overfitting'] > 50:
        print("  ⚠️ High overfitting - consider regularization")
    elif best_result['overfitting'] < -10:
        print("  ⚠️ Underfitting - consider more complex model")
    else:
        print("  ✅ Good bias-variance balance")
    
    if best_result['cv_std'] > 20:
        print("  ⚠️ High variance across folds - model may be unstable")
    else:
        print("  ✅ Stable cross-validation performance")
    
    print(f"\n🎯 SUBMISSION ANALYSIS:")
    print(f"  Predictions generated: {submission_stats['count']:,}")
    print(f"  Value range: {submission_stats['min']:.2f} - {submission_stats['max']:.2f}")
    print(f"  Average prediction: {submission_stats['mean']:.2f}")
    print(f"  Prediction diversity: {submission_stats['std']:.2f}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if best_result['val_r2'] > 0.8:
        print("  🎯 Strong baseline achieved!")
        print("  📈 Ready for advanced models (XGBoost, Neural Networks)")
        print("  🔧 Consider hyperparameter tuning")
    elif best_result['val_r2'] > 0.6:
        print("  ✅ Decent baseline performance")
        print("  🛠️ Focus on feature engineering")
        print("  📊 Try ensemble methods")
    else:
        print("  ⚠️ Baseline needs improvement")
        print("  🔍 Review data preprocessing")
        print("  🧪 Try different algorithms")
    
    print(f"\n🏁 COMPETITION READINESS:")
    mse_score = best_result['val_mse']
    if mse_score < 100:
        readiness = "🏆 EXCELLENT - Ready for submission!"
    elif mse_score < 500:
        readiness = "✅ GOOD - Competitive baseline"
    elif mse_score < 1000:
        readiness = "📈 FAIR - Needs improvement"
    else:
        readiness = "⚠️ POOR - Significant work needed"
    
    print(f"  Status: {readiness}")
    print(f"  MSE Score: {mse_score:.2f}")
    
    return {
        'best_model': best_model_name,
        'competition_mse': best_result['val_mse'],
        'model_r2': best_result['val_r2'],
        'cv_stability': best_result['cv_std'],
        'readiness_score': mse_score
    }

def main():
    """Main baseline model evaluation pipeline"""
    print("BTK DATATHON 2025 - BASELINE MODEL EVALUATION")
    print("Simple Algorithms with MSE Competition Metric")
    print("=" * 60)
    
    # Load and prepare data
    train_sessions, test_sessions = load_and_prepare_simple_data()
    
    # Evaluate baseline models
    results, X_train, X_val, y_train, y_val, feature_cols = evaluate_baseline_models(
        train_sessions, test_sessions
    )
    
    # Performance analysis
    comparison_df, best_model_name = create_performance_analysis(results)
    
    # Create visualizations
    create_visualizations(results, X_val, y_val, best_model_name)
    
    # Feature importance analysis
    best_model = results[best_model_name]['model']
    importance_df = analyze_feature_importance(best_model, feature_cols)
    
    # Generate predictions
    submission_df = generate_predictions(best_model_name, results, test_sessions, feature_cols)
    
    # Get submission statistics
    submission_stats = {
        'count': len(submission_df),
        'min': submission_df['session_value'].min(),
        'max': submission_df['session_value'].max(),
        'mean': submission_df['session_value'].mean(),
        'std': submission_df['session_value'].std()
    }
    
    # Generate final report
    final_report = generate_final_report(results, comparison_df, best_model_name, submission_stats)
    
    # Save results
    comparison_df.to_csv('baseline_model_results.csv', index=False)
    if importance_df is not None:
        importance_df.to_csv('baseline_feature_importance.csv', index=False)
    
    print(f"\n🎉 BASELINE EVALUATION COMPLETE!")
    print(f"📁 Files saved:")
    print(f"  - corrected_baseline_submission.csv (competition submission)")
    print(f"  - baseline_model_results.csv (model comparison)")
    print(f"  - baseline_feature_importance.csv (feature analysis)")
    
    return {
        'results': results,
        'comparison_df': comparison_df,
        'best_model_name': best_model_name,
        'submission_df': submission_df,
        'final_report': final_report,
        'feature_importance': importance_df
    }

if __name__ == "__main__":
    baseline_evaluation = main()
