# BTK Datathon 2025 - Exploratory Data Analysis Report

## Dataset Overview

### Data Structure
- **Training Data**: 141,219 rows × 7 columns (50.96 MB)
- **Test Data**: 62,951 rows × 6 columns (22.23 MB)
- **Sample Submission**: 30,789 rows × 2 columns

### Data Quality
✅ **No missing values** in either training or test datasets
✅ **Consistent data types** across train/test sets
✅ **Clean data structure** with proper formatting

## Target Variable Analysis (`session_value`)

### Key Statistics
- **Range**: 5.38 to 2,328.66
- **Mean**: 75.35
- **Median**: 40.95
- **Standard Deviation**: 121.79
- **Skewness**: 8.73 (highly right-skewed)
- **Kurtosis**: 118.89 (heavy-tailed distribution)

### Distribution Characteristics
- **Highly skewed** distribution with long right tail
- **12,719 unique values** indicating continuous target
- **No class imbalance issues** (continuous regression problem)
- **Log transformation recommended** due to high skewness

## Feature Analysis

### Categorical Features

#### 1. Event Type (4 unique values)
- **VIEW**: 58,829 (41.7%) - Most common event
- **ADD_CART**: 42,304 (30.0%)
- **REMOVE_CART**: 25,615 (18.1%)
- **BUY**: 14,471 (10.2%) - Least common but most valuable

#### 2. Product ID (26,470 unique values)
- **High cardinality** - 18.7% unique in training
- **Top product**: PROD_005510 (359 occurrences)
- **Recommendation**: Use target encoding or frequency encoding

#### 3. Category ID (448 unique values)
- **Moderate cardinality** - 0.3% unique
- **Top category**: CAT_00090 (6,861 occurrences)
- **Good candidate** for one-hot encoding or target encoding

#### 4. User ID (51,821 unique values)
- **Very high cardinality** - 36.7% unique
- **Top user**: USER_026471 (134 events)
- **Recommendation**: Use user-based aggregations

#### 5. User Session (70,736 unique values)
- **Extremely high cardinality** - 50.1% unique
- **Top session**: SESSION_165310 (116 events)
- **Key insight**: This is the prediction target grouping

### Temporal Features

#### Event Time Analysis
- **Training period**: June 1-21, 2025 (21 days)
- **Test period**: June 22-30, 2025 (9 days)
- **Clear temporal split** for time series validation

#### Temporal Patterns
- **Hourly distribution**: Relatively uniform across hours
- **Day of week**: Slight variations but no strong patterns
- **Daily trend**: Consistent activity across the period

## Data Consistency

### Train vs Test Comparison
- **Common columns**: 6 (all except session_value)
- **Data types**: Consistent across datasets
- **Feature distributions**: Similar patterns observed
- **No data leakage**: Clean temporal split

### Key Differences
- **Test data lacks target variable** (session_value)
- **Different time periods** (train: June 1-21, test: June 22-30)
- **Fewer unique values** in test set due to shorter period

## Feature Engineering Recommendations

### 1. Session-Level Aggregations
```python
# Aggregate by user_session
- event_count_per_session
- unique_products_per_session
- unique_categories_per_session
- session_duration
- buy_events_count
- view_to_cart_ratio
```

### 2. User-Level Features
```python
# Aggregate by user_id
- user_total_events
- user_avg_session_value
- user_favorite_category
- user_activity_frequency
```

### 3. Product/Category Features
```python
# Product popularity and pricing
- product_popularity_score
- category_avg_session_value
- product_conversion_rate
```

### 4. Temporal Features
```python
# Time-based features
- hour_of_day
- day_of_week
- is_weekend
- time_since_first_event
```

### 5. Behavioral Features
```python
# User behavior patterns
- cart_abandonment_rate
- purchase_conversion_rate
- session_engagement_score
```

## Key Insights

### 1. Business Understanding
- This is an **e-commerce session value prediction** problem
- Need to predict the total value of user sessions
- Events represent user journey: VIEW → ADD_CART → REMOVE_CART → BUY

### 2. Data Characteristics
- **Continuous regression** problem (not classification)
- **High cardinality** categorical features require careful encoding
- **Session-based** prediction requires aggregation strategies
- **Temporal structure** allows for time-based validation

### 3. Modeling Considerations
- **Target transformation**: Log transformation recommended
- **Feature encoding**: Mix of target encoding and aggregations
- **Validation strategy**: Time-based split (respect temporal order)
- **Model types**: Tree-based models likely to perform well

### 4. Potential Challenges
- **High cardinality** features may cause overfitting
- **Skewed target** distribution requires careful handling
- **Session aggregation** complexity
- **Cold start** problem for new users/products in test set

## Next Steps

1. **Feature Engineering**: Implement session-level aggregations
2. **Target Transformation**: Apply log transformation
3. **Encoding Strategy**: Implement target encoding for high-cardinality features
4. **Model Selection**: Start with tree-based models (XGBoost, LightGBM)
5. **Validation**: Implement time-based cross-validation
6. **Hyperparameter Tuning**: Optimize for regression metrics (RMSE, MAE)

## Correlation Analysis

- **Low correlations** between temporal features and target
- **No multicollinearity** issues detected
- **Feature independence** suggests ensemble methods may work well

---

*Analysis completed on BTK Datathon 2025 dataset*
*Total processing time: ~5 minutes*
