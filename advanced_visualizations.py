#!/usr/bin/env python3
"""
Advanced Visualizations for BTK Datathon 2025
Deep dive into data patterns and relationships
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_and_prepare_data():
    """Load and prepare data for advanced analysis"""
    print("Loading and preparing data...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    # Convert datetime
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    test_df['event_time'] = pd.to_datetime(test_df['event_time'])
    
    # Extract time features
    for df in [train_df, test_df]:
        df['hour'] = df['event_time'].dt.hour
        df['day_of_week'] = df['event_time'].dt.dayofweek
        df['day'] = df['event_time'].dt.day
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
    
    return train_df, test_df

def session_value_deep_dive(train_df):
    """Deep analysis of session values"""
    print("\n" + "="*60)
    print("SESSION VALUE DEEP DIVE ANALYSIS")
    print("="*60)
    
    # Session-level aggregation
    session_stats = train_df.groupby('user_session').agg({
        'session_value': 'first',  # Same for all events in session
        'event_type': 'count',     # Total events per session
        'product_id': 'nunique',   # Unique products per session
        'category_id': 'nunique',  # Unique categories per session
        'user_id': 'first'         # User for this session
    }).rename(columns={
        'event_type': 'total_events',
        'product_id': 'unique_products',
        'category_id': 'unique_categories'
    })
    
    print(f"Total unique sessions: {len(session_stats)}")
    print(f"Session value statistics:")
    print(session_stats['session_value'].describe())
    
    # Visualizations
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. Session value distribution
    session_stats['session_value'].hist(bins=100, ax=axes[0,0], alpha=0.7)
    axes[0,0].set_title('Session Value Distribution')
    axes[0,0].set_xlabel('Session Value')
    axes[0,0].set_ylabel('Frequency')
    
    # 2. Log-transformed session value
    log_values = np.log1p(session_stats['session_value'])
    log_values.hist(bins=50, ax=axes[0,1], alpha=0.7, color='orange')
    axes[0,1].set_title('Log-Transformed Session Value')
    axes[0,1].set_xlabel('Log(Session Value + 1)')
    axes[0,1].set_ylabel('Frequency')
    
    # 3. Events per session vs session value
    axes[0,2].scatter(session_stats['total_events'], session_stats['session_value'], 
                     alpha=0.5, s=10)
    axes[0,2].set_title('Events per Session vs Session Value')
    axes[0,2].set_xlabel('Total Events per Session')
    axes[0,2].set_ylabel('Session Value')
    
    # 4. Unique products vs session value
    axes[1,0].scatter(session_stats['unique_products'], session_stats['session_value'], 
                     alpha=0.5, s=10, color='green')
    axes[1,0].set_title('Unique Products vs Session Value')
    axes[1,0].set_xlabel('Unique Products per Session')
    axes[1,0].set_ylabel('Session Value')
    
    # 5. Session value by number of events (boxplot)
    session_stats['event_bins'] = pd.cut(session_stats['total_events'], 
                                       bins=[0, 1, 2, 5, 10, float('inf')], 
                                       labels=['1', '2', '3-5', '6-10', '10+'])
    session_stats.boxplot(column='session_value', by='event_bins', ax=axes[1,1])
    axes[1,1].set_title('Session Value by Event Count Groups')
    axes[1,1].set_xlabel('Number of Events in Session')
    axes[1,1].set_ylabel('Session Value')
    
    # 6. Top 20 sessions by value
    top_sessions = session_stats.nlargest(20, 'session_value')
    top_sessions['session_value'].plot(kind='bar', ax=axes[1,2])
    axes[1,2].set_title('Top 20 Sessions by Value')
    axes[1,2].set_xlabel('Session Rank')
    axes[1,2].set_ylabel('Session Value')
    axes[1,2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    return session_stats

def event_type_analysis(train_df):
    """Analyze event types and their relationship with session values"""
    print("\n" + "="*60)
    print("EVENT TYPE ANALYSIS")
    print("="*60)
    
    # Event type distribution by session value
    event_session_stats = train_df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
    event_session_stats['session_value'] = train_df.groupby('user_session')['session_value'].first()
    
    # Calculate conversion funnel
    funnel_stats = train_df['event_type'].value_counts()
    print("Event Type Funnel:")
    for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
        if event_type in funnel_stats:
            print(f"{event_type}: {funnel_stats[event_type]:,} ({funnel_stats[event_type]/len(train_df)*100:.1f}%)")
    
    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Event type distribution
    funnel_stats.plot(kind='bar', ax=axes[0,0], color=['skyblue', 'lightgreen', 'salmon', 'gold'])
    axes[0,0].set_title('Event Type Distribution')
    axes[0,0].set_xlabel('Event Type')
    axes[0,0].set_ylabel('Count')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Session value by event type presence
    for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
        if event_type in event_session_stats.columns:
            has_event = event_session_stats[event_type] > 0
            values_with = event_session_stats[has_event]['session_value']
            values_without = event_session_stats[~has_event]['session_value']
            
            axes[0,1].hist(values_with, alpha=0.5, label=f'With {event_type}', bins=50)
            
    axes[0,1].set_title('Session Value Distribution by Event Type')
    axes[0,1].set_xlabel('Session Value')
    axes[0,1].set_ylabel('Frequency')
    axes[0,1].legend()
    axes[0,1].set_yscale('log')
    
    # 3. Average session value by event type combinations
    event_combinations = []
    avg_values = []
    
    for _, row in event_session_stats.iterrows():
        combo = []
        for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
            if event_type in row and row[event_type] > 0:
                combo.append(event_type)
        
        if combo:
            combo_str = ' + '.join(combo)
            event_combinations.append(combo_str)
            avg_values.append(row['session_value'])
    
    combo_df = pd.DataFrame({'combination': event_combinations, 'session_value': avg_values})
    combo_avg = combo_df.groupby('combination')['session_value'].agg(['mean', 'count']).reset_index()
    combo_avg = combo_avg[combo_avg['count'] >= 10]  # Filter for combinations with at least 10 occurrences
    combo_avg = combo_avg.sort_values('mean', ascending=False).head(10)
    
    combo_avg.set_index('combination')['mean'].plot(kind='bar', ax=axes[1,0])
    axes[1,0].set_title('Average Session Value by Event Combinations (Top 10)')
    axes[1,0].set_xlabel('Event Combination')
    axes[1,0].set_ylabel('Average Session Value')
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # 4. Event count vs session value correlation
    for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
        if event_type in event_session_stats.columns:
            x = event_session_stats[event_type]
            y = event_session_stats['session_value']
            correlation = x.corr(y)
            
            axes[1,1].scatter(x, y, alpha=0.3, label=f'{event_type} (r={correlation:.3f})', s=10)
    
    axes[1,1].set_title('Event Counts vs Session Value')
    axes[1,1].set_xlabel('Event Count')
    axes[1,1].set_ylabel('Session Value')
    axes[1,1].legend()
    axes[1,1].set_yscale('log')
    
    plt.tight_layout()
    plt.show()
    
    return event_session_stats

def temporal_patterns_analysis(train_df, test_df):
    """Analyze temporal patterns in the data"""
    print("\n" + "="*60)
    print("TEMPORAL PATTERNS ANALYSIS")
    print("="*60)
    
    # Combine datasets for temporal analysis
    train_df['dataset'] = 'train'
    test_df['dataset'] = 'test'
    test_df['session_value'] = np.nan  # Add missing column
    
    combined_df = pd.concat([train_df, test_df], ignore_index=True)
    
    # Daily patterns
    daily_stats = combined_df.groupby([combined_df['event_time'].dt.date, 'dataset']).agg({
        'event_type': 'count',
        'user_session': 'nunique',
        'user_id': 'nunique'
    }).rename(columns={
        'event_type': 'total_events',
        'user_session': 'unique_sessions',
        'user_id': 'unique_users'
    })
    
    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Daily event volume
    train_daily = daily_stats.loc[daily_stats.index.get_level_values('dataset') == 'train']
    test_daily = daily_stats.loc[daily_stats.index.get_level_values('dataset') == 'test']
    
    train_dates = train_daily.index.get_level_values(0)
    test_dates = test_daily.index.get_level_values(0)
    
    axes[0,0].plot(train_dates, train_daily['total_events'], 'b-', label='Train', marker='o')
    axes[0,0].plot(test_dates, test_daily['total_events'], 'r-', label='Test', marker='s')
    axes[0,0].set_title('Daily Event Volume')
    axes[0,0].set_xlabel('Date')
    axes[0,0].set_ylabel('Total Events')
    axes[0,0].legend()
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Hourly patterns
    hourly_train = train_df.groupby('hour')['session_value'].agg(['mean', 'count'])
    hourly_train['mean'].plot(kind='bar', ax=axes[0,1], color='skyblue')
    axes[0,1].set_title('Average Session Value by Hour (Training)')
    axes[0,1].set_xlabel('Hour of Day')
    axes[0,1].set_ylabel('Average Session Value')
    
    # 3. Day of week patterns
    dow_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    dow_train = train_df.groupby('day_of_week')['session_value'].agg(['mean', 'count'])
    dow_train.index = [dow_names[i] for i in dow_train.index]
    dow_train['mean'].plot(kind='bar', ax=axes[1,0], color='lightgreen')
    axes[1,0].set_title('Average Session Value by Day of Week')
    axes[1,0].set_xlabel('Day of Week')
    axes[1,0].set_ylabel('Average Session Value')
    
    # 4. Weekend vs Weekday
    weekend_stats = train_df.groupby('is_weekend')['session_value'].agg(['mean', 'count', 'std'])
    weekend_stats.index = ['Weekday', 'Weekend']
    weekend_stats['mean'].plot(kind='bar', ax=axes[1,1], color=['orange', 'purple'])
    axes[1,1].set_title('Session Value: Weekend vs Weekday')
    axes[1,1].set_xlabel('Day Type')
    axes[1,1].set_ylabel('Average Session Value')
    
    plt.tight_layout()
    plt.show()
    
    print(f"\nTemporal Insights:")
    print(f"Training period: {train_df['event_time'].min()} to {train_df['event_time'].max()}")
    print(f"Test period: {test_df['event_time'].min()} to {test_df['event_time'].max()}")
    print(f"Weekend vs Weekday average session value:")
    print(weekend_stats[['mean', 'count']])

def main():
    """Main function for advanced visualizations"""
    print("BTK DATATHON 2025 - ADVANCED VISUALIZATIONS")
    print("=" * 60)
    
    # Load data
    train_df, test_df = load_and_prepare_data()
    
    # Run analyses
    session_stats = session_value_deep_dive(train_df)
    event_stats = event_type_analysis(train_df)
    temporal_patterns_analysis(train_df, test_df)
    
    print("\n" + "="*60)
    print("ADVANCED ANALYSIS COMPLETE!")
    print("="*60)
    
    return session_stats, event_stats

if __name__ == "__main__":
    session_stats, event_stats = main()
