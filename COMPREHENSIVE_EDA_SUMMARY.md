# BTK Datathon 2025 - Comprehensive EDA Summary

## 🎯 Executive Summary

This comprehensive exploratory data analysis reveals a **session value prediction problem** for an e-commerce platform. The dataset contains user interaction events (VIEW, ADD_CART, REMOVE_CART, BUY) with the goal of predicting the total monetary value of user sessions.

### Key Findings
- **Clean dataset** with no missing values
- **Highly skewed target** variable requiring transformation
- **Strong predictive signal** from purchase behavior (R² = 0.80 with basic features)
- **Session-level aggregations** are most predictive
- **Temporal patterns** exist but are secondary to behavioral features

---

## 📊 Dataset Overview

| Metric | Training Data | Test Data |
|--------|---------------|-----------|
| **Rows** | 141,219 | 62,951 |
| **Columns** | 7 | 6 |
| **Unique Sessions** | 70,736 | 30,789 |
| **Time Period** | June 1-21, 2025 | June 22-30, 2025 |
| **Memory Usage** | 50.96 MB | 22.23 MB |

### Data Quality ✅
- **Zero missing values** across all columns
- **Consistent data types** between train/test
- **Proper temporal split** (no data leakage)
- **Well-formatted** timestamps and identifiers

---

## 🎯 Target Variable Analysis

### Session Value Distribution
- **Range**: 5.38 to 2,328.66
- **Mean**: 75.35 (event-level) / 42.20 (session-level)
- **Median**: 40.95 (event-level) / 30.75 (session-level)
- **Skewness**: 8.73 (highly right-skewed)
- **Unique Values**: 12,719 (continuous target)

### Key Insights
- **Highly skewed** distribution with long right tail
- **Log transformation recommended** for modeling
- **No class imbalance** (continuous regression problem)
- **Wide value range** suggests diverse user behaviors

---

## 🔍 Feature Analysis

### Event Type Distribution (Conversion Funnel)
1. **VIEW**: 58,829 events (41.7%) - Top of funnel
2. **ADD_CART**: 42,304 events (30.0%) - Intent signal
3. **REMOVE_CART**: 25,615 events (18.1%) - Hesitation signal
4. **BUY**: 14,471 events (10.2%) - Conversion signal

**Conversion Rate**: ~10.2% from view to purchase

### High Cardinality Features
| Feature | Unique Values | Cardinality | Encoding Strategy |
|---------|---------------|-------------|-------------------|
| **user_session** | 70,736 | 50.1% | Target for prediction |
| **user_id** | 51,821 | 36.7% | User-level aggregations |
| **product_id** | 26,470 | 18.7% | Target/frequency encoding |
| **category_id** | 448 | 0.3% | One-hot/target encoding |
| **event_type** | 4 | <0.1% | One-hot encoding |

---

## ⭐ Feature Importance Analysis

### Top Predictive Features (Random Forest R² = 0.80)
1. **buy_count** (74.9%) - Dominant predictor
2. **session_duration_minutes** (5.6%) - Engagement signal
3. **add_cart_count** (4.7%) - Intent signal
4. **unique_products** (2.9%) - Diversity signal
5. **total_events** (2.5%) - Activity signal

### Behavioral Features
- **view_to_cart_ratio**: Conversion intent
- **cart_to_buy_ratio**: Purchase completion
- **remove_to_cart_ratio**: Hesitation indicator

### Temporal Features (Lower Importance)
- **hour**: 2.3% importance
- **day_of_week**: 1.1% importance
- **is_weekend**: 0.2% importance

---

## 👥 User Behavior Analysis

### User Segments
- **High Value**: 12,950 users (25.0%) - Premium customers
- **Medium Value**: 25,897 users (50.0%) - Regular customers  
- **Low Value**: 12,957 users (25.0%) - Occasional users

### User Statistics
- **Average sessions per user**: 1.37
- **Average session value**: 40.64
- **Conversion rate**: 9.2% (view to buy)
- **Cart conversion rate**: 10.3% (cart to buy)

### Power Users
- **Most active user**: 47 sessions
- **Highest session value**: 2,328.66
- **Top 1% users** drive significant value

---

## ⏰ Temporal Patterns

### Training vs Test Periods
- **Training**: June 1-21, 2025 (21 days)
- **Test**: June 22-30, 2025 (9 days)
- **Clean temporal split** for realistic evaluation

### Temporal Insights
- **Weekend effect**: 8.9% higher average session values on weekends
- **Hourly patterns**: Relatively uniform distribution
- **Daily trends**: Consistent activity levels

---

## 🛠️ Feature Engineering Recommendations

### 1. Session-Level Aggregations (Highest Priority)
```python
# Event counts by type
- view_count, add_cart_count, remove_cart_count, buy_count

# Session characteristics  
- total_events, unique_products, unique_categories
- session_duration_minutes, events_per_minute

# Behavioral ratios
- view_to_cart_ratio, cart_to_buy_ratio, remove_to_cart_ratio
```

### 2. User-Level Features
```python
# User history and preferences
- user_total_sessions, user_avg_session_value
- user_favorite_category, user_conversion_rate
- user_activity_recency, user_lifetime_value
```

### 3. Product/Category Features
```python
# Popularity and performance metrics
- product_popularity_score, category_avg_value
- product_conversion_rate, category_engagement_score
```

### 4. Advanced Features
```python
# Sequence and timing
- time_between_events, event_sequence_patterns
- session_stage_durations, abandonment_signals
```

---

## 🤖 Modeling Recommendations

### Model Selection
1. **Tree-based models** (Primary recommendation)
   - XGBoost, LightGBM, CatBoost
   - Handle high cardinality naturally
   - Robust to outliers

2. **Neural Networks** (Secondary)
   - Embedding layers for categorical features
   - Deep learning for complex patterns

3. **Ensemble Methods** (Advanced)
   - Combine tree-based + neural networks
   - Stacking/blending approaches

### Target Transformation
```python
# Recommended transformation
target_transformed = np.log1p(session_value)
```

### Validation Strategy
1. **Time-based split** (respect temporal order)
2. **Group K-fold by user_id** (prevent leakage)
3. **Stratified sampling** by session value ranges

### Evaluation Metrics
- **Primary**: RMSE (Root Mean Square Error)
- **Secondary**: MAE (Mean Absolute Error)
- **Business**: MAPE (Mean Absolute Percentage Error)

---

## ⚠️ Potential Challenges

### 1. Data Challenges
- **High cardinality** features may cause overfitting
- **Skewed target** requires careful handling
- **Cold start** problem for new users/products

### 2. Modeling Challenges
- **Feature engineering complexity** for session aggregations
- **Memory requirements** for high-cardinality encoding
- **Validation complexity** with temporal and user constraints

### 3. Business Challenges
- **Interpretability** vs performance trade-offs
- **Real-time prediction** requirements
- **Concept drift** over time

---

## 🎯 Next Steps

### Immediate Actions
1. **Implement session-level feature engineering**
2. **Apply log transformation to target**
3. **Set up time-based validation framework**
4. **Baseline model with top features**

### Advanced Development
1. **Hyperparameter optimization**
2. **Feature selection and engineering iteration**
3. **Ensemble model development**
4. **Cross-validation and robustness testing**

### Model Deployment Considerations
1. **Feature pipeline automation**
2. **Real-time inference optimization**
3. **Model monitoring and drift detection**
4. **A/B testing framework**

---

## 📈 Expected Performance

Based on the EDA analysis:
- **Baseline Random Forest**: R² = 0.80, RMSE = 22.13
- **Expected improvement** with feature engineering: R² > 0.85
- **Target performance** with advanced models: R² > 0.90

---

*Analysis completed: BTK Datathon 2025*  
*Dataset: E-commerce Session Value Prediction*  
*Total analysis time: ~15 minutes*
