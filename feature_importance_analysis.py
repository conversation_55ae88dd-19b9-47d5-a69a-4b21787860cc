#!/usr/bin/env python3
"""
Feature Importance and Modeling Insights for BTK Datathon 2025
Analysis of feature relationships and modeling recommendations
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import <PERSON>ForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def create_session_features(df):
    """Create session-level features for modeling"""
    print("Creating session-level features...")
    
    # Session-level aggregations
    session_features = df.groupby('user_session').agg({
        'event_time': ['min', 'max', 'count'],
        'event_type': lambda x: x.value_counts().to_dict(),
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'session_value': 'first'  # Same for all events in session
    }).reset_index()
    
    # Flatten column names
    session_features.columns = ['user_session', 'first_event_time', 'last_event_time', 
                               'total_events', 'event_type_counts', 'unique_products', 
                               'unique_categories', 'user_id', 'session_value']
    
    # Calculate session duration in minutes
    session_features['session_duration_minutes'] = (
        session_features['last_event_time'] - session_features['first_event_time']
    ).dt.total_seconds() / 60
    
    # Extract event type counts
    event_types = ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']
    for event_type in event_types:
        session_features[f'{event_type.lower()}_count'] = session_features['event_type_counts'].apply(
            lambda x: x.get(event_type, 0) if isinstance(x, dict) else 0
        )
    
    # Calculate ratios and behavioral features
    session_features['view_to_cart_ratio'] = np.where(
        session_features['view_count'] > 0,
        session_features['add_cart_count'] / session_features['view_count'],
        0
    )
    
    session_features['cart_to_buy_ratio'] = np.where(
        session_features['add_cart_count'] > 0,
        session_features['buy_count'] / session_features['add_cart_count'],
        0
    )
    
    session_features['remove_to_cart_ratio'] = np.where(
        session_features['add_cart_count'] > 0,
        session_features['remove_cart_count'] / session_features['add_cart_count'],
        0
    )
    
    # Time-based features
    session_features['hour'] = session_features['first_event_time'].dt.hour
    session_features['day_of_week'] = session_features['first_event_time'].dt.dayofweek
    session_features['is_weekend'] = session_features['day_of_week'].isin([5, 6]).astype(int)
    
    # Drop intermediate columns
    session_features = session_features.drop(['event_type_counts', 'first_event_time', 'last_event_time'], axis=1)
    
    return session_features

def analyze_feature_importance(session_features):
    """Analyze feature importance using Random Forest"""
    print("\n" + "="*60)
    print("FEATURE IMPORTANCE ANALYSIS")
    print("="*60)
    
    # Prepare features for modeling
    feature_cols = [col for col in session_features.columns 
                   if col not in ['user_session', 'session_value', 'user_id']]
    
    X = session_features[feature_cols].copy()
    y = session_features['session_value'].copy()
    
    # Handle any remaining categorical variables
    categorical_cols = X.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        le = LabelEncoder()
        X[col] = le.fit_transform(X[col].astype(str))
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train Random Forest
    rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(X_train, y_train)
    
    # Predictions and metrics
    y_pred = rf.predict(X_test)
    
    rmse = np.sqrt(mean_squared_error(y_test, y_pred))
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    print(f"Random Forest Performance:")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r2:.4f}")
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\nTop 15 Most Important Features:")
    print(feature_importance.head(15))
    
    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Feature importance plot
    top_features = feature_importance.head(15)
    top_features.plot(x='feature', y='importance', kind='barh', ax=axes[0,0])
    axes[0,0].set_title('Top 15 Feature Importance (Random Forest)')
    axes[0,0].set_xlabel('Importance')
    
    # 2. Actual vs Predicted
    axes[0,1].scatter(y_test, y_pred, alpha=0.5, s=10)
    axes[0,1].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    axes[0,1].set_xlabel('Actual Session Value')
    axes[0,1].set_ylabel('Predicted Session Value')
    axes[0,1].set_title(f'Actual vs Predicted (R² = {r2:.3f})')
    
    # 3. Residuals plot
    residuals = y_test - y_pred
    axes[1,0].scatter(y_pred, residuals, alpha=0.5, s=10)
    axes[1,0].axhline(y=0, color='r', linestyle='--')
    axes[1,0].set_xlabel('Predicted Session Value')
    axes[1,0].set_ylabel('Residuals')
    axes[1,0].set_title('Residuals Plot')
    
    # 4. Feature correlation with target
    correlations = X.corrwith(y).abs().sort_values(ascending=False)
    correlations.head(15).plot(kind='bar', ax=axes[1,1])
    axes[1,1].set_title('Top 15 Feature Correlations with Target')
    axes[1,1].set_xlabel('Features')
    axes[1,1].set_ylabel('Absolute Correlation')
    axes[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    return feature_importance, rf, X, y

def user_behavior_analysis(session_features):
    """Analyze user behavior patterns"""
    print("\n" + "="*60)
    print("USER BEHAVIOR ANALYSIS")
    print("="*60)
    
    # User-level aggregations
    user_stats = session_features.groupby('user_id').agg({
        'session_value': ['count', 'mean', 'sum', 'std'],
        'total_events': 'mean',
        'unique_products': 'mean',
        'buy_count': 'sum',
        'view_count': 'sum',
        'add_cart_count': 'sum'
    }).reset_index()
    
    # Flatten column names
    user_stats.columns = ['user_id', 'session_count', 'avg_session_value', 'total_session_value',
                         'session_value_std', 'avg_events_per_session', 'avg_unique_products',
                         'total_buys', 'total_views', 'total_add_carts']
    
    # Calculate user behavior metrics
    user_stats['total_conversion_rate'] = np.where(
        user_stats['total_views'] > 0,
        user_stats['total_buys'] / user_stats['total_views'],
        0
    )
    
    user_stats['cart_conversion_rate'] = np.where(
        user_stats['total_add_carts'] > 0,
        user_stats['total_buys'] / user_stats['total_add_carts'],
        0
    )
    
    # User segments based on behavior
    user_stats['user_segment'] = 'Low Value'
    user_stats.loc[user_stats['avg_session_value'] > user_stats['avg_session_value'].quantile(0.75), 'user_segment'] = 'High Value'
    user_stats.loc[(user_stats['avg_session_value'] > user_stats['avg_session_value'].quantile(0.25)) & 
                   (user_stats['avg_session_value'] <= user_stats['avg_session_value'].quantile(0.75)), 'user_segment'] = 'Medium Value'
    
    print(f"User Segments:")
    print(user_stats['user_segment'].value_counts())
    print(f"\nUser Behavior Statistics:")
    print(user_stats[['session_count', 'avg_session_value', 'total_conversion_rate', 'cart_conversion_rate']].describe())
    
    # Visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. User session count distribution
    user_stats['session_count'].hist(bins=50, ax=axes[0,0], alpha=0.7)
    axes[0,0].set_title('Distribution of Sessions per User')
    axes[0,0].set_xlabel('Number of Sessions')
    axes[0,0].set_ylabel('Number of Users')
    axes[0,0].set_yscale('log')
    
    # 2. Average session value by user segment
    user_stats.boxplot(column='avg_session_value', by='user_segment', ax=axes[0,1])
    axes[0,1].set_title('Average Session Value by User Segment')
    axes[0,1].set_xlabel('User Segment')
    axes[0,1].set_ylabel('Average Session Value')
    
    # 3. Conversion rates distribution
    user_stats['total_conversion_rate'].hist(bins=50, ax=axes[1,0], alpha=0.7)
    axes[1,0].set_title('Distribution of User Conversion Rates')
    axes[1,0].set_xlabel('Conversion Rate')
    axes[1,0].set_ylabel('Number of Users')
    
    # 4. Sessions vs Total Value
    axes[1,1].scatter(user_stats['session_count'], user_stats['total_session_value'], alpha=0.5, s=10)
    axes[1,1].set_xlabel('Number of Sessions')
    axes[1,1].set_ylabel('Total Session Value')
    axes[1,1].set_title('User Sessions vs Total Value')
    axes[1,1].set_xscale('log')
    axes[1,1].set_yscale('log')
    
    plt.tight_layout()
    plt.show()
    
    return user_stats

def generate_modeling_recommendations(feature_importance, session_features):
    """Generate specific modeling recommendations"""
    print("\n" + "="*60)
    print("MODELING RECOMMENDATIONS")
    print("="*60)
    
    print("🎯 KEY INSIGHTS FOR MODELING:")
    print("\n1. TARGET VARIABLE:")
    print("   - Highly right-skewed distribution (skewness = 8.73)")
    print("   - Recommendation: Apply log transformation → log(session_value + 1)")
    print("   - Consider robust regression techniques")
    
    print("\n2. TOP PREDICTIVE FEATURES:")
    top_5_features = feature_importance.head(5)['feature'].tolist()
    for i, feature in enumerate(top_5_features, 1):
        importance = feature_importance.head(5).iloc[i-1]['importance']
        print(f"   {i}. {feature} (importance: {importance:.4f})")
    
    print("\n3. FEATURE ENGINEERING PRIORITIES:")
    print("   - Session-level aggregations are most important")
    print("   - Event type counts and ratios show high predictive power")
    print("   - Temporal features (hour, day_of_week) have moderate importance")
    print("   - User behavior patterns (conversion rates) are valuable")
    
    print("\n4. MODEL RECOMMENDATIONS:")
    print("   - Tree-based models (XGBoost, LightGBM, CatBoost) - handle high cardinality well")
    print("   - Neural networks with embeddings for categorical features")
    print("   - Ensemble methods combining different model types")
    
    print("\n5. VALIDATION STRATEGY:")
    print("   - Time-based split (respect temporal order)")
    print("   - Group K-fold by user_id to prevent data leakage")
    print("   - Monitor for overfitting due to high cardinality features")
    
    print("\n6. EVALUATION METRICS:")
    print("   - Primary: RMSE (Root Mean Square Error)")
    print("   - Secondary: MAE (Mean Absolute Error)")
    print("   - Consider MAPE for business interpretation")
    
    # Data quality summary
    print(f"\n7. DATA QUALITY SUMMARY:")
    print(f"   - No missing values ✅")
    print(f"   - {len(session_features)} unique sessions")
    print(f"   - Session value range: {session_features['session_value'].min():.2f} - {session_features['session_value'].max():.2f}")
    print(f"   - Average session value: {session_features['session_value'].mean():.2f}")

def main():
    """Main function for feature importance analysis"""
    print("BTK DATATHON 2025 - FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)
    
    # Load data
    train_df = pd.read_csv('train.csv')
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    
    # Create session-level features
    session_features = create_session_features(train_df)
    
    # Analyze feature importance
    feature_importance, rf_model, X, y = analyze_feature_importance(session_features)
    
    # User behavior analysis
    user_stats = user_behavior_analysis(session_features)
    
    # Generate recommendations
    generate_modeling_recommendations(feature_importance, session_features)
    
    print("\n" + "="*60)
    print("FEATURE IMPORTANCE ANALYSIS COMPLETE!")
    print("="*60)
    
    return session_features, feature_importance, user_stats

if __name__ == "__main__":
    session_features, feature_importance, user_stats = main()
