#!/usr/bin/env python3
"""
Robust Advanced Models Evaluation for BTK Datathon 2025
XGBoost, LightGBM, CatBoost, Neural Networks with comprehensive comparison
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPRegressor
import warnings
warnings.filterwarnings('ignore')

# Import advanced libraries
import xgboost as xgb
import lightgbm as lgb
import catboost as cb

class RobustAdvancedEvaluator:
    """
    Robust evaluation of advanced ML models with comprehensive analysis
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.results = {}
        
    def load_best_data(self):
        """Load the best available preprocessed data"""
        print("Loading best available data...")
        
        # Try enhanced features first, then advanced, then basic
        data_sources = [
            ('enhanced', 'advanced_train_processed.csv', 'advanced_test_processed.csv'),
            ('advanced', 'preprocessed_data/train_preprocessed.csv', 'preprocessed_data/test_preprocessed.csv')
        ]
        
        for data_type, train_file, test_file in data_sources:
            try:
                train_df = pd.read_csv(train_file)
                test_df = pd.read_csv(test_file)
                print(f"Loaded {data_type} data: Train {train_df.shape}, Test {test_df.shape}")
                return train_df, test_df, data_type
            except FileNotFoundError:
                continue
        
        # Fallback: run preprocessing
        print("Running preprocessing pipeline...")
        from data_preprocessing import main as preprocess_main
        data = preprocess_main()
        
        train_df = data['X_train'].copy()
        train_df['session_value'] = data['y_train']
        train_df['user_session'] = data['train_ids']
        
        test_df = data['X_test'].copy()
        test_df['user_session'] = data['test_ids']
        
        return train_df, test_df, 'runtime'
    
    def prepare_modeling_data(self, train_df, test_df):
        """Prepare data for advanced modeling"""
        print("Preparing data for modeling...")
        
        # Identify features
        exclude_cols = ['session_value', 'user_session', 'session_value_original']
        feature_cols = [col for col in train_df.columns if col not in exclude_cols]
        
        # Prepare datasets
        X = train_df[feature_cols]
        y = train_df['session_value']
        X_test = test_df[feature_cols]
        test_sessions = test_df['user_session']
        
        # Train/validation split
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=self.random_state
        )
        
        print(f"Data prepared: {len(feature_cols)} features, {len(X_train)} train, {len(X_val)} val")
        
        return X_train, X_val, y_train, y_val, X_test, test_sessions, feature_cols
    
    def evaluate_advanced_models(self, X_train, X_val, y_train, y_val):
        """Evaluate all advanced models"""
        print("\n" + "="*60)
        print("ADVANCED MODEL EVALUATION")
        print("="*60)
        
        # Initialize models
        models = {
            'Random Forest': RandomForestRegressor(
                n_estimators=200, max_depth=15, min_samples_split=10,
                random_state=self.random_state, n_jobs=-1
            ),
            'Gradient Boosting': GradientBoostingRegressor(
                n_estimators=200, max_depth=8, learning_rate=0.1,
                random_state=self.random_state
            ),
            'XGBoost': xgb.XGBRegressor(
                n_estimators=200, max_depth=8, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8,
                random_state=self.random_state, n_jobs=-1
            ),
            'LightGBM': lgb.LGBMRegressor(
                n_estimators=200, max_depth=8, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8,
                random_state=self.random_state, n_jobs=-1, verbose=-1
            ),
            'CatBoost': cb.CatBoostRegressor(
                iterations=200, depth=8, learning_rate=0.1,
                random_seed=self.random_state, verbose=False
            ),
            'Neural Network': MLPRegressor(
                hidden_layer_sizes=(128, 64, 32), activation='relu',
                solver='adam', alpha=0.001, learning_rate='adaptive',
                max_iter=500, random_state=self.random_state
            )
        }
        
        # Evaluate each model
        for name, model in models.items():
            print(f"\n🔄 Evaluating {name}...")
            
            try:
                # Handle scaling for neural networks
                if name == 'Neural Network':
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_val_scaled = scaler.transform(X_val)
                    
                    model.fit(X_train_scaled, y_train)
                    y_train_pred = model.predict(X_train_scaled)
                    y_val_pred = model.predict(X_val_scaled)
                    
                    # Cross-validation with scaling
                    X_full = pd.concat([pd.DataFrame(X_train), pd.DataFrame(X_val)])
                    y_full = pd.concat([pd.Series(y_train), pd.Series(y_val)])
                    X_full_scaled = scaler.fit_transform(X_full)
                    cv_scores = cross_val_score(model, X_full_scaled, y_full, cv=3, 
                                              scoring='neg_mean_squared_error')
                else:
                    # Regular training
                    model.fit(X_train, y_train)
                    y_train_pred = model.predict(X_train)
                    y_val_pred = model.predict(X_val)
                    
                    # Cross-validation
                    X_full = pd.concat([pd.DataFrame(X_train), pd.DataFrame(X_val)])
                    y_full = pd.concat([pd.Series(y_train), pd.Series(y_val)])
                    cv_scores = cross_val_score(model, X_full, y_full, cv=3, 
                                              scoring='neg_mean_squared_error')
                
                # Calculate metrics
                train_mse = mean_squared_error(y_train, y_train_pred)
                val_mse = mean_squared_error(y_val, y_val_pred)
                train_r2 = r2_score(y_train, y_train_pred)
                val_r2 = r2_score(y_val, y_val_pred)
                cv_mse = -cv_scores.mean()
                cv_std = cv_scores.std()
                
                # Store results
                self.results[name] = {
                    'model': model,
                    'scaler': scaler if name == 'Neural Network' else None,
                    'train_mse': train_mse,
                    'val_mse': val_mse,
                    'train_r2': train_r2,
                    'val_r2': val_r2,
                    'cv_mse': cv_mse,
                    'cv_std': cv_std,
                    'overfitting': val_mse - train_mse
                }
                
                # Print results
                print(f"  🎯 Validation MSE: {val_mse:.6f}")
                print(f"  📊 Validation R²: {val_r2:.6f}")
                print(f"  🔄 CV MSE: {cv_mse:.6f} ± {cv_std:.6f}")
                print(f"  📈 Overfitting: {val_mse - train_mse:.6f}")
                
            except Exception as e:
                print(f"  ❌ Failed to evaluate {name}: {e}")
                continue
        
        return self.results
    
    def create_performance_comparison(self):
        """Create comprehensive performance comparison"""
        print("\n" + "="*60)
        print("PERFORMANCE COMPARISON & RANKING")
        print("="*60)
        
        # Create comparison DataFrame
        comparison_data = []
        for name, result in self.results.items():
            comparison_data.append({
                'Model': name,
                'Validation MSE': result['val_mse'],
                'Validation R²': result['val_r2'],
                'CV MSE': result['cv_mse'],
                'CV Std': result['cv_std'],
                'Overfitting': result['overfitting'],
                'Training MSE': result['train_mse']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('Validation MSE')
        
        print("🏆 ADVANCED MODEL RANKING (by Validation MSE):")
        print("=" * 80)
        
        medals = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣", "6️⃣"]
        for i, row in comparison_df.iterrows():
            medal = medals[i] if i < len(medals) else f"{i+1}."
            print(f"{medal} {row['Model']:<20} | MSE: {row['Validation MSE']:>10.6f} | "
                  f"R²: {row['Validation R²']:>8.6f} | CV: {row['CV MSE']:>10.6f}")
        
        return comparison_df
    
    def analyze_best_model(self, comparison_df, X_val, y_val):
        """Detailed analysis of the best performing model"""
        print("\n" + "="*60)
        print("BEST MODEL ANALYSIS")
        print("="*60)
        
        best_model_name = comparison_df.iloc[0]['Model']
        best_result = self.results[best_model_name]
        best_model = best_result['model']
        
        print(f"🏆 CHAMPION MODEL: {best_model_name}")
        print(f"📊 Performance Metrics:")
        print(f"  Competition MSE: {best_result['val_mse']:.6f}")
        print(f"  R² Score: {best_result['val_r2']:.6f}")
        print(f"  Cross-validation: {best_result['cv_mse']:.6f} ± {best_result['cv_std']:.6f}")
        print(f"  Overfitting: {best_result['overfitting']:.6f}")
        
        # Model-specific analysis
        if hasattr(best_model, 'feature_importances_'):
            print(f"\n📈 Feature Importance Analysis:")
            # This will be handled in visualization
            pass
        
        # Performance assessment
        if best_result['val_r2'] > 0.95:
            assessment = "🏆 OUTSTANDING"
        elif best_result['val_r2'] > 0.90:
            assessment = "🎯 EXCELLENT"
        elif best_result['val_r2'] > 0.85:
            assessment = "✅ VERY GOOD"
        elif best_result['val_r2'] > 0.80:
            assessment = "📈 GOOD"
        else:
            assessment = "⚠️ NEEDS IMPROVEMENT"
        
        print(f"  Assessment: {assessment}")
        
        # Stability analysis
        if best_result['cv_std'] < 0.001:
            stability = "🎯 VERY STABLE"
        elif best_result['cv_std'] < 0.01:
            stability = "✅ STABLE"
        elif best_result['cv_std'] < 0.1:
            stability = "📊 MODERATE"
        else:
            stability = "⚠️ UNSTABLE"
        
        print(f"  Stability: {stability}")
        
        return best_model_name, best_result
    
    def create_visualizations(self, comparison_df, X_val, y_val):
        """Create comprehensive performance visualizations"""
        print("\nCreating performance visualizations...")
        
        # Get best model for detailed plots
        best_model_name = comparison_df.iloc[0]['Model']
        best_model = self.results[best_model_name]['model']
        best_scaler = self.results[best_model_name]['scaler']
        
        # Prepare validation data
        if best_scaler is not None:
            X_val_model = best_scaler.transform(X_val)
        else:
            X_val_model = X_val
        
        y_val_pred = best_model.predict(X_val_model)
        
        # Create plots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. Model MSE Comparison
        comparison_df.plot(x='Model', y='Validation MSE', kind='bar', ax=axes[0,0], 
                          color='skyblue', legend=False)
        axes[0,0].set_title('Validation MSE Comparison')
        axes[0,0].set_ylabel('MSE')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # 2. Model R² Comparison
        comparison_df.plot(x='Model', y='Validation R²', kind='bar', ax=axes[0,1], 
                          color='lightgreen', legend=False)
        axes[0,1].set_title('Validation R² Comparison')
        axes[0,1].set_ylabel('R²')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # 3. Cross-validation stability
        axes[0,2].errorbar(range(len(comparison_df)), comparison_df['CV MSE'], 
                          yerr=comparison_df['CV Std'], fmt='o', capsize=5)
        axes[0,2].set_xticks(range(len(comparison_df)))
        axes[0,2].set_xticklabels(comparison_df['Model'], rotation=45)
        axes[0,2].set_title('Cross-Validation MSE ± Std')
        axes[0,2].set_ylabel('CV MSE')
        
        # 4. Actual vs Predicted (best model)
        axes[1,0].scatter(y_val, y_val_pred, alpha=0.5, s=10)
        axes[1,0].plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 'r--', lw=2)
        axes[1,0].set_xlabel('Actual')
        axes[1,0].set_ylabel('Predicted')
        axes[1,0].set_title(f'Actual vs Predicted - {best_model_name}')
        
        # 5. Residuals plot
        residuals = y_val - y_val_pred
        axes[1,1].scatter(y_val_pred, residuals, alpha=0.5, s=10)
        axes[1,1].axhline(y=0, color='r', linestyle='--')
        axes[1,1].set_xlabel('Predicted')
        axes[1,1].set_ylabel('Residuals')
        axes[1,1].set_title(f'Residuals Plot - {best_model_name}')
        
        # 6. Feature importance (if available)
        if hasattr(best_model, 'feature_importances_'):
            feature_cols = [col for col in X_val.columns]
            importance_df = pd.DataFrame({
                'feature': feature_cols,
                'importance': best_model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            top_features = importance_df.head(15)
            axes[1,2].barh(range(len(top_features)), top_features['importance'])
            axes[1,2].set_yticks(range(len(top_features)))
            axes[1,2].set_yticklabels(top_features['feature'])
            axes[1,2].set_xlabel('Importance')
            axes[1,2].set_title(f'Top 15 Features - {best_model_name}')
            axes[1,2].invert_yaxis()
        else:
            axes[1,2].text(0.5, 0.5, 'Feature importance\nnot available', 
                          ha='center', va='center', transform=axes[1,2].transAxes)
            axes[1,2].set_title('Feature Importance')
        
        plt.tight_layout()
        plt.show()
        
        return importance_df if hasattr(best_model, 'feature_importances_') else None
    
    def generate_predictions(self, best_model_name, X_test, test_sessions):
        """Generate predictions using the best model"""
        print(f"\n🔮 Generating predictions using {best_model_name}...")
        
        best_result = self.results[best_model_name]
        best_model = best_result['model']
        best_scaler = best_result['scaler']
        
        # Prepare test data
        if best_scaler is not None:
            X_test_model = best_scaler.transform(X_test)
        else:
            X_test_model = X_test
        
        # Generate predictions
        test_predictions = best_model.predict(X_test_model)
        
        # Transform back to original scale (assuming log1p was used)
        test_predictions_orig = np.expm1(test_predictions)
        
        # Create submission
        submission_df = pd.DataFrame({
            'user_session': test_sessions,
            'session_value': test_predictions_orig
        })
        
        # Save submission
        submission_filename = f'advanced_{best_model_name.lower().replace(" ", "_")}_submission.csv'
        submission_df.to_csv(submission_filename, index=False)
        
        print(f"✅ Predictions saved to {submission_filename}")
        print(f"📊 Prediction Statistics:")
        print(f"  Count: {len(submission_df):,}")
        print(f"  Min: {submission_df['session_value'].min():.2f}")
        print(f"  Max: {submission_df['session_value'].max():.2f}")
        print(f"  Mean: {submission_df['session_value'].mean():.2f}")
        print(f"  Median: {submission_df['session_value'].median():.2f}")
        print(f"  Std: {submission_df['session_value'].std():.2f}")
        
        return submission_df, submission_filename
    
    def generate_comprehensive_report(self, comparison_df, best_model_name, importance_df):
        """Generate final comprehensive report"""
        print("\n" + "="*60)
        print("COMPREHENSIVE ADVANCED MODEL REPORT")
        print("="*60)
        
        best_result = self.results[best_model_name]
        
        print(f"🏆 CHAMPION MODEL: {best_model_name}")
        print(f"📊 COMPETITION PERFORMANCE:")
        print(f"  Validation MSE: {best_result['val_mse']:.6f}")
        print(f"  Validation R²: {best_result['val_r2']:.6f}")
        print(f"  Cross-validation MSE: {best_result['cv_mse']:.6f} ± {best_result['cv_std']:.6f}")
        
        # Model comparison
        print(f"\n📈 TOP 3 MODELS:")
        medals = ["🥇", "🥈", "🥉"]
        for i, row in comparison_df.head(3).iterrows():
            medal = medals[i] if i < len(medals) else f"{i+1}."
            print(f"  {medal} {row['Model']}: MSE = {row['Validation MSE']:.6f}, R² = {row['Validation R²']:.6f}")
        
        # Feature importance insights
        if importance_df is not None:
            print(f"\n🔍 TOP 5 PREDICTIVE FEATURES:")
            for i, row in importance_df.head(5).iterrows():
                print(f"  {i+1}. {row['feature']}: {row['importance']:.4f}")
        
        # Performance assessment
        mse_score = best_result['val_mse']
        r2_score = best_result['val_r2']
        
        print(f"\n🎯 COMPETITION READINESS:")
        if r2_score > 0.95:
            readiness = "🏆 OUTSTANDING - Top tier performance!"
        elif r2_score > 0.90:
            readiness = "🎯 EXCELLENT - Highly competitive!"
        elif r2_score > 0.85:
            readiness = "✅ VERY GOOD - Strong performance"
        elif r2_score > 0.80:
            readiness = "📈 GOOD - Competitive baseline"
        else:
            readiness = "⚠️ NEEDS IMPROVEMENT"
        
        print(f"  Status: {readiness}")
        print(f"  MSE Score: {mse_score:.6f}")
        print(f"  Variance Explained: {r2_score*100:.2f}%")
        
        # Recommendations
        print(f"\n💡 NEXT STEPS:")
        if r2_score > 0.92:
            print("  🔧 Hyperparameter tuning for final optimization")
            print("  🎭 Ensemble methods for marginal gains")
            print("  📊 Feature selection for efficiency")
        elif r2_score > 0.85:
            print("  🛠️ Feature engineering improvements")
            print("  🎛️ Hyperparameter optimization")
            print("  🤝 Model ensembling")
        else:
            print("  🔍 Review preprocessing pipeline")
            print("  🧪 Try different architectures")
            print("  📈 Focus on feature quality")
        
        return {
            'best_model': best_model_name,
            'best_mse': best_result['val_mse'],
            'best_r2': best_result['val_r2'],
            'cv_stability': best_result['cv_std'],
            'comparison_df': comparison_df
        }

def main():
    """Main advanced model evaluation pipeline"""
    print("BTK DATATHON 2025 - ADVANCED MODEL EVALUATION")
    print("XGBoost | LightGBM | CatBoost | Neural Networks")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = RobustAdvancedEvaluator()
    
    # Load best available data
    train_df, test_df, data_type = evaluator.load_best_data()
    
    # Prepare data
    X_train, X_val, y_train, y_val, X_test, test_sessions, feature_cols = evaluator.prepare_modeling_data(
        train_df, test_df
    )
    
    # Evaluate advanced models
    results = evaluator.evaluate_advanced_models(X_train, X_val, y_train, y_val)
    
    if not results:
        print("❌ No models were successfully evaluated!")
        return None
    
    # Performance comparison
    comparison_df = evaluator.create_performance_comparison()
    
    # Best model analysis
    best_model_name, best_result = evaluator.analyze_best_model(comparison_df, X_val, y_val)
    
    # Create visualizations
    importance_df = evaluator.create_visualizations(comparison_df, X_val, y_val)
    
    # Generate predictions
    submission_df, submission_filename = evaluator.generate_predictions(
        best_model_name, X_test, test_sessions
    )
    
    # Generate comprehensive report
    final_report = evaluator.generate_comprehensive_report(
        comparison_df, best_model_name, importance_df
    )
    
    # Save results
    comparison_df.to_csv('advanced_model_comparison.csv', index=False)
    if importance_df is not None:
        importance_df.to_csv('advanced_feature_importance.csv', index=False)
    
    print(f"\n🎉 ADVANCED MODEL EVALUATION COMPLETE!")
    print(f"📁 Files saved:")
    print(f"  - {submission_filename} (best model submission)")
    print(f"  - advanced_model_comparison.csv (performance comparison)")
    print(f"  - advanced_feature_importance.csv (feature analysis)")
    
    return {
        'evaluator': evaluator,
        'results': results,
        'comparison_df': comparison_df,
        'best_model_name': best_model_name,
        'submission_df': submission_df,
        'final_report': final_report,
        'importance_df': importance_df,
        'data_type': data_type
    }

if __name__ == "__main__":
    advanced_evaluation = main()
