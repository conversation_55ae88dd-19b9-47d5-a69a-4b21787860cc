#!/usr/bin/env python3
"""
Advanced Feature Engineering for BTK Datathon 2025
Innovative techniques to boost model performance beyond baseline
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import PolynomialFeatures
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureEngineer:
    """
    Advanced feature engineering pipeline with innovative techniques
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.feature_stats = {}
        self.encoders = {}
        self.clusters = {}
        
    def create_sequence_features(self, df):
        """Create features from event sequences within sessions"""
        print("Creating sequence-based features...")
        
        # Sort by session and time
        df_sorted = df.sort_values(['user_session', 'event_time'])
        
        sequence_features = []
        
        for session_id, session_data in df_sorted.groupby('user_session'):
            session_features = {'user_session': session_id}
            
            # Event sequence patterns
            event_sequence = session_data['event_type'].tolist()
            session_features['sequence_length'] = len(event_sequence)
            
            # First and last event types
            session_features['first_event'] = event_sequence[0] if event_sequence else 'UNKNOWN'
            session_features['last_event'] = event_sequence[-1] if event_sequence else 'UNKNOWN'
            
            # Event transitions (bigrams)
            transitions = []
            for i in range(len(event_sequence) - 1):
                transitions.append(f"{event_sequence[i]}_{event_sequence[i+1]}")
            
            # Count specific transition patterns
            session_features['view_to_cart_transitions'] = transitions.count('VIEW_ADD_CART')
            session_features['cart_to_buy_transitions'] = transitions.count('ADD_CART_BUY')
            session_features['cart_to_remove_transitions'] = transitions.count('ADD_CART_REMOVE_CART')
            session_features['remove_to_cart_transitions'] = transitions.count('REMOVE_CART_ADD_CART')
            
            # Sequence complexity (unique transitions)
            session_features['unique_transitions'] = len(set(transitions))
            session_features['transition_entropy'] = self._calculate_entropy(transitions)
            
            # Time-based sequence features
            if len(session_data) > 1:
                time_diffs = session_data['event_time'].diff().dt.total_seconds().dropna()
                session_features['avg_time_between_events'] = time_diffs.mean()
                session_features['std_time_between_events'] = time_diffs.std()
                session_features['min_time_between_events'] = time_diffs.min()
                session_features['max_time_between_events'] = time_diffs.max()
            else:
                session_features['avg_time_between_events'] = 0
                session_features['std_time_between_events'] = 0
                session_features['min_time_between_events'] = 0
                session_features['max_time_between_events'] = 0
            
            # Product exploration patterns
            products = session_data['product_id'].tolist()
            categories = session_data['category_id'].tolist()
            
            session_features['product_revisits'] = len(products) - len(set(products))
            session_features['category_switches'] = sum(1 for i in range(len(categories)-1) 
                                                      if categories[i] != categories[i+1])
            
            # Funnel progression score
            has_view = 'VIEW' in event_sequence
            has_cart = 'ADD_CART' in event_sequence
            has_remove = 'REMOVE_CART' in event_sequence
            has_buy = 'BUY' in event_sequence
            
            funnel_score = 0
            if has_view: funnel_score += 1
            if has_cart: funnel_score += 2
            if has_buy: funnel_score += 4
            if has_remove: funnel_score -= 1  # Negative signal
            
            session_features['funnel_progression_score'] = funnel_score
            
            sequence_features.append(session_features)
        
        return pd.DataFrame(sequence_features)
    
    def _calculate_entropy(self, sequence):
        """Calculate entropy of a sequence"""
        if not sequence:
            return 0
        
        from collections import Counter
        counts = Counter(sequence)
        total = len(sequence)
        
        entropy = 0
        for count in counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)
        
        return entropy
    
    def create_temporal_features(self, df):
        """Advanced temporal feature engineering"""
        print("Creating advanced temporal features...")
        
        df = df.copy()
        df['event_time'] = pd.to_datetime(df['event_time'])
        
        # Cyclical encoding for temporal features
        df['hour_sin'] = np.sin(2 * np.pi * df['event_time'].dt.hour / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['event_time'].dt.hour / 24)
        df['day_of_week_sin'] = np.sin(2 * np.pi * df['event_time'].dt.dayofweek / 7)
        df['day_of_week_cos'] = np.cos(2 * np.pi * df['event_time'].dt.dayofweek / 7)
        df['day_of_month_sin'] = np.sin(2 * np.pi * df['event_time'].dt.day / 31)
        df['day_of_month_cos'] = np.cos(2 * np.pi * df['event_time'].dt.day / 31)
        
        # Business hours and time periods
        df['is_business_hours'] = ((df['event_time'].dt.hour >= 9) & 
                                  (df['event_time'].dt.hour <= 17)).astype(int)
        df['is_evening'] = ((df['event_time'].dt.hour >= 18) & 
                           (df['event_time'].dt.hour <= 22)).astype(int)
        df['is_night'] = ((df['event_time'].dt.hour >= 23) | 
                         (df['event_time'].dt.hour <= 5)).astype(int)
        df['is_morning'] = ((df['event_time'].dt.hour >= 6) & 
                           (df['event_time'].dt.hour <= 11)).astype(int)
        df['is_afternoon'] = ((df['event_time'].dt.hour >= 12) & 
                             (df['event_time'].dt.hour <= 17)).astype(int)
        
        # Week patterns
        df['is_monday'] = (df['event_time'].dt.dayofweek == 0).astype(int)
        df['is_friday'] = (df['event_time'].dt.dayofweek == 4).astype(int)
        df['is_weekend'] = (df['event_time'].dt.dayofweek.isin([5, 6])).astype(int)
        
        # Time since epoch (trend feature)
        df['timestamp_normalized'] = (df['event_time'] - df['event_time'].min()).dt.total_seconds()
        df['timestamp_normalized'] = df['timestamp_normalized'] / df['timestamp_normalized'].max()
        
        return df
    
    def create_categorical_features(self, df, is_train=True):
        """Advanced categorical feature engineering"""
        print(f"Creating advanced categorical features for {'train' if is_train else 'test'} data...")
        
        df = df.copy()
        
        # Product and category popularity features
        if is_train:
            # Calculate popularity scores
            self.feature_stats['product_popularity'] = df['product_id'].value_counts().to_dict()
            self.feature_stats['category_popularity'] = df['category_id'].value_counts().to_dict()
            
            # Calculate conversion rates by product/category
            if 'session_value' in df.columns:
                product_values = df.groupby('product_id')['session_value'].agg(['mean', 'std', 'count'])
                category_values = df.groupby('category_id')['session_value'].agg(['mean', 'std', 'count'])
                
                self.feature_stats['product_avg_value'] = product_values['mean'].to_dict()
                self.feature_stats['product_value_std'] = product_values['std'].fillna(0).to_dict()
                self.feature_stats['category_avg_value'] = category_values['mean'].to_dict()
                self.feature_stats['category_value_std'] = category_values['std'].fillna(0).to_dict()
        
        # Apply popularity features
        df['product_popularity'] = df['product_id'].map(
            self.feature_stats.get('product_popularity', {})).fillna(1)
        df['category_popularity'] = df['category_id'].map(
            self.feature_stats.get('category_popularity', {})).fillna(1)
        
        # Apply value-based features
        df['product_avg_value'] = df['product_id'].map(
            self.feature_stats.get('product_avg_value', {})).fillna(df.get('session_value', 0).mean() if is_train else 0)
        df['category_avg_value'] = df['category_id'].map(
            self.feature_stats.get('category_avg_value', {})).fillna(df.get('session_value', 0).mean() if is_train else 0)
        
        # Logarithmic transformations for skewed popularity
        df['product_popularity_log'] = np.log1p(df['product_popularity'])
        df['category_popularity_log'] = np.log1p(df['category_popularity'])
        
        # Product/Category ID embeddings (hash-based)
        df['product_id_hash'] = df['product_id'].apply(lambda x: hash(x) % 1000)
        df['category_id_hash'] = df['category_id'].apply(lambda x: hash(x) % 100)
        
        return df
    
    def create_interaction_features(self, session_df):
        """Create interaction features between important variables"""
        print("Creating interaction features...")
        
        df = session_df.copy()
        
        # Behavioral interactions
        df['buy_rate_x_duration'] = df.get('buy_count', 0) * df.get('session_duration_minutes', 0)
        df['events_per_product'] = df.get('total_events', 1) / (df.get('unique_products', 1) + 1e-6)
        df['events_per_category'] = df.get('total_events', 1) / (df.get('unique_categories', 1) + 1e-6)
        
        # Conversion efficiency features
        df['conversion_efficiency'] = (df.get('buy_count', 0) + 1) / (df.get('total_events', 1) + 1)
        df['cart_efficiency'] = (df.get('add_cart_count', 0) + 1) / (df.get('view_count', 1) + 1)
        df['buy_efficiency'] = (df.get('buy_count', 0) + 1) / (df.get('add_cart_count', 1) + 1)
        
        # Time-based interactions
        if 'hour' in df.columns:
            df['weekend_evening'] = df.get('is_weekend', 0) * df.get('is_evening', 0)
            df['weekday_business'] = (1 - df.get('is_weekend', 0)) * df.get('is_business_hours', 0)
        
        # Product diversity interactions
        df['product_category_ratio'] = (df.get('unique_products', 1) + 1) / (df.get('unique_categories', 1) + 1)
        df['exploration_intensity'] = df.get('unique_products', 0) * df.get('session_duration_minutes', 0)
        
        return df
    
    def create_user_clustering_features(self, session_df, n_clusters=10):
        """Create user behavior clusters"""
        print(f"Creating user clustering features with {n_clusters} clusters...")
        
        # Features for clustering
        cluster_features = [
            'total_events', 'unique_products', 'unique_categories',
            'session_duration_minutes', 'buy_count', 'view_count', 'add_cart_count'
        ]
        
        # Get available features
        available_features = [f for f in cluster_features if f in session_df.columns]
        
        if len(available_features) < 3:
            print("Not enough features for clustering, skipping...")
            return session_df
        
        # Prepare data for clustering
        cluster_data = session_df[available_features].fillna(0)
        
        # Fit KMeans
        kmeans = KMeans(n_clusters=n_clusters, random_state=self.random_state, n_init=10)
        session_df = session_df.copy()
        session_df['user_behavior_cluster'] = kmeans.fit_predict(cluster_data)
        
        # Store cluster model
        self.clusters['user_behavior'] = kmeans
        
        # Create cluster-based features
        for i in range(n_clusters):
            session_df[f'is_cluster_{i}'] = (session_df['user_behavior_cluster'] == i).astype(int)
        
        # Cluster distance features
        cluster_centers = kmeans.cluster_centers_
        distances = kmeans.transform(cluster_data)
        
        session_df['cluster_distance_min'] = distances.min(axis=1)
        session_df['cluster_distance_mean'] = distances.mean(axis=1)
        session_df['cluster_distance_std'] = distances.std(axis=1)
        
        return session_df
    
    def create_polynomial_features(self, df, degree=2, max_features=20):
        """Create polynomial features for top important features"""
        print(f"Creating polynomial features (degree={degree})...")
        
        # Select top numerical features for polynomial expansion
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        # Remove target and identifier columns
        exclude_cols = ['session_value', 'user_session', 'session_value_original']
        numerical_cols = [col for col in numerical_cols if col not in exclude_cols]
        
        # Limit to top features to avoid explosion
        if len(numerical_cols) > max_features:
            # Use variance as a simple feature selection criterion
            variances = df[numerical_cols].var().sort_values(ascending=False)
            numerical_cols = variances.head(max_features).index.tolist()
        
        print(f"Creating polynomial features for: {numerical_cols[:5]}... ({len(numerical_cols)} total)")
        
        # Create polynomial features
        poly = PolynomialFeatures(degree=degree, include_bias=False, interaction_only=True)
        poly_features = poly.fit_transform(df[numerical_cols])
        
        # Get feature names
        poly_feature_names = poly.get_feature_names_out(numerical_cols)
        
        # Create DataFrame with polynomial features
        poly_df = pd.DataFrame(poly_features, columns=poly_feature_names, index=df.index)
        
        # Remove original features (they're included in polynomial)
        new_features = [col for col in poly_df.columns if col not in numerical_cols]
        
        # Limit number of new features to prevent overfitting
        if len(new_features) > 50:
            # Select features with highest variance
            feature_vars = poly_df[new_features].var().sort_values(ascending=False)
            new_features = feature_vars.head(50).index.tolist()
        
        print(f"Added {len(new_features)} polynomial features")
        
        # Combine with original dataframe
        result_df = df.copy()
        for feature in new_features:
            result_df[f'poly_{feature}'] = poly_df[feature]
        
        return result_df
    
    def create_statistical_features(self, session_df):
        """Create statistical aggregation features"""
        print("Creating statistical features...")
        
        df = session_df.copy()
        
        # Rolling statistics (if we have time-ordered data)
        numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        exclude_cols = ['session_value', 'user_session']
        numerical_cols = [col for col in numerical_cols if col not in exclude_cols]
        
        # Percentile features
        for col in numerical_cols[:10]:  # Limit to avoid too many features
            if col in df.columns:
                col_values = df[col]
                df[f'{col}_percentile_rank'] = col_values.rank(pct=True)
                df[f'{col}_zscore'] = (col_values - col_values.mean()) / (col_values.std() + 1e-6)
                df[f'{col}_is_outlier'] = (np.abs(df[f'{col}_zscore']) > 2).astype(int)
        
        return df
    
    def apply_advanced_feature_engineering(self, train_df, test_df=None):
        """Apply all advanced feature engineering techniques"""
        print("\n" + "="*60)
        print("ADVANCED FEATURE ENGINEERING PIPELINE")
        print("="*60)
        
        # 1. Create sequence features
        train_sequence = self.create_sequence_features(train_df)
        test_sequence = self.create_sequence_features(test_df) if test_df is not None else None
        
        # 2. Advanced temporal features
        train_temporal = self.create_temporal_features(train_df)
        test_temporal = self.create_temporal_features(test_df) if test_df is not None else None
        
        # 3. Advanced categorical features
        train_categorical = self.create_categorical_features(train_temporal, is_train=True)
        test_categorical = self.create_categorical_features(test_temporal, is_train=False) if test_temporal is not None else None
        
        # Aggregate to session level for remaining steps
        train_sessions = self._aggregate_to_session_level(train_categorical, train_sequence)
        test_sessions = self._aggregate_to_session_level(test_categorical, test_sequence) if test_categorical is not None else None
        
        # 4. Interaction features
        train_sessions = self.create_interaction_features(train_sessions)
        test_sessions = self.create_interaction_features(test_sessions) if test_sessions is not None else None
        
        # 5. User clustering
        train_sessions = self.create_user_clustering_features(train_sessions)
        if test_sessions is not None:
            test_sessions = self._apply_clustering_to_test(test_sessions)
        
        # 6. Statistical features
        train_sessions = self.create_statistical_features(train_sessions)
        test_sessions = self.create_statistical_features(test_sessions) if test_sessions is not None else None
        
        # 7. Polynomial features (careful with this - can explode feature space)
        # train_sessions = self.create_polynomial_features(train_sessions, degree=2)
        # test_sessions = self.create_polynomial_features(test_sessions, degree=2) if test_sessions is not None else None
        
        print(f"\nAdvanced Feature Engineering Complete!")
        print(f"Training sessions: {train_sessions.shape}")
        if test_sessions is not None:
            print(f"Test sessions: {test_sessions.shape}")
        
        return train_sessions, test_sessions
    
    def _aggregate_to_session_level(self, event_df, sequence_df):
        """Aggregate event-level features to session level"""
        # Basic aggregations
        session_agg = event_df.groupby('user_session').agg({
            'event_time': ['min', 'max', 'count'],
            'product_id': 'nunique',
            'category_id': 'nunique',
            'user_id': 'first',
            'product_popularity': ['mean', 'max', 'min'],
            'category_popularity': ['mean', 'max', 'min'],
            'product_avg_value': ['mean', 'max', 'min'],
            'category_avg_value': ['mean', 'max', 'min'],
            'hour_sin': 'first',
            'hour_cos': 'first',
            'day_of_week_sin': 'first',
            'day_of_week_cos': 'first',
            'is_business_hours': 'max',
            'is_evening': 'max',
            'is_weekend': 'first'
        })
        
        # Flatten column names
        session_agg.columns = ['_'.join(col).strip() for col in session_agg.columns]
        session_agg = session_agg.reset_index()
        
        # Add target if available
        if 'session_value' in event_df.columns:
            target_values = event_df.groupby('user_session')['session_value'].first()
            session_agg = session_agg.merge(target_values.reset_index(), on='user_session', how='left')
        
        # Merge with sequence features
        if sequence_df is not None:
            session_agg = session_agg.merge(sequence_df, on='user_session', how='left')
        
        return session_agg
    
    def _apply_clustering_to_test(self, test_sessions):
        """Apply fitted clustering model to test data"""
        if 'user_behavior' not in self.clusters:
            return test_sessions
        
        cluster_features = [
            'total_events', 'unique_products', 'unique_categories',
            'session_duration_minutes', 'buy_count', 'view_count', 'add_cart_count'
        ]
        
        available_features = [f for f in cluster_features if f in test_sessions.columns]
        
        if len(available_features) < 3:
            return test_sessions
        
        cluster_data = test_sessions[available_features].fillna(0)
        kmeans = self.clusters['user_behavior']
        
        test_sessions = test_sessions.copy()
        test_sessions['user_behavior_cluster'] = kmeans.predict(cluster_data)
        
        # Create cluster indicator features
        n_clusters = len(kmeans.cluster_centers_)
        for i in range(n_clusters):
            test_sessions[f'is_cluster_{i}'] = (test_sessions['user_behavior_cluster'] == i).astype(int)
        
        # Cluster distance features
        distances = kmeans.transform(cluster_data)
        test_sessions['cluster_distance_min'] = distances.min(axis=1)
        test_sessions['cluster_distance_mean'] = distances.mean(axis=1)
        test_sessions['cluster_distance_std'] = distances.std(axis=1)
        
        return test_sessions

def integrate_with_baseline_preprocessing(train_advanced, test_advanced):
    """Integrate advanced features with baseline preprocessing pipeline"""
    print("\n" + "="*60)
    print("INTEGRATING WITH BASELINE PREPROCESSING")
    print("="*60)

    from data_preprocessing import SessionDataPreprocessor

    # Initialize baseline preprocessor
    preprocessor = SessionDataPreprocessor()

    # Load original data for user features
    train_orig = pd.read_csv('train.csv')
    test_orig = pd.read_csv('test.csv')

    # Create baseline session features
    train_baseline = preprocessor.create_session_features(train_orig, is_train=True)
    test_baseline = preprocessor.create_session_features(test_orig, is_train=False)

    # Create user features
    train_user_features = preprocessor.create_user_features(train_baseline, is_train=True)
    test_user_features = preprocessor.create_user_features(test_baseline, is_train=False)

    # Merge baseline user features with advanced features
    train_combined = train_advanced.merge(train_user_features, on='user_id_first', how='left')
    test_combined = test_advanced.merge(test_user_features, on='user_id_first', how='left')

    # Handle missing values
    train_combined = preprocessor.handle_missing_values(train_combined)
    test_combined = preprocessor.handle_missing_values(test_combined)

    # Handle outliers and transform target
    if 'session_value' in train_combined.columns:
        train_combined = preprocessor.detect_and_handle_outliers(train_combined, 'session_value')
        train_combined = preprocessor.apply_target_transformation(train_combined, 'session_value')

    # Encode remaining categorical features
    train_combined, test_combined = preprocessor.encode_categorical_features(train_combined, test_combined)

    # Scale features
    train_combined, test_combined = preprocessor.scale_numerical_features(train_combined, test_combined)

    # Finalize features
    train_final = preprocessor.remove_unnecessary_columns(train_combined)
    test_final = preprocessor.remove_unnecessary_columns(test_combined)

    # Prepare final datasets
    exclude_cols = ['session_value', 'session_value_original', 'user_session']
    feature_cols = [col for col in train_final.columns if col not in exclude_cols]

    X_train = train_final[feature_cols]
    y_train = train_final['session_value'] if 'session_value' in train_final.columns else None
    X_test = test_final[feature_cols]

    train_ids = train_final['user_session']
    test_ids = test_final['user_session']

    print(f"Final integrated features:")
    print(f"Training: {X_train.shape}")
    print(f"Test: {X_test.shape}")
    print(f"Total features: {len(feature_cols)}")

    return X_train, y_train, X_test, train_ids, test_ids, preprocessor, feature_cols

def evaluate_advanced_features(X_train, y_train, feature_cols, preprocessor):
    """Evaluate the performance of advanced features"""
    print("\n" + "="*60)
    print("EVALUATING ADVANCED FEATURES")
    print("="*60)

    from sklearn.ensemble import RandomForestRegressor
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.metrics import mean_squared_error, r2_score

    # Split for validation
    X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )

    # Train Random Forest
    rf = RandomForestRegressor(n_estimators=200, random_state=42, n_jobs=-1)
    rf.fit(X_train_split, y_train_split)

    # Predictions
    y_val_pred = rf.predict(X_val_split)

    # Metrics on transformed scale
    val_mse = mean_squared_error(y_val_split, y_val_pred)
    val_r2 = r2_score(y_val_split, y_val_pred)

    # Transform back to original scale
    y_val_orig = preprocessor.inverse_transform_target(y_val_split)
    y_val_pred_orig = preprocessor.inverse_transform_target(y_val_pred)
    val_mse_orig = mean_squared_error(y_val_orig, y_val_pred_orig)

    print(f"Advanced Features Performance:")
    print(f"Validation MSE (transformed): {val_mse:.6f}")
    print(f"Validation MSE (original): {val_mse_orig:.2f}")
    print(f"Validation R²: {val_r2:.6f}")

    # Cross-validation
    cv_scores = cross_val_score(rf, X_train, y_train, cv=5, scoring='neg_mean_squared_error', n_jobs=-1)
    cv_mse = -cv_scores.mean()
    cv_std = cv_scores.std()

    print(f"Cross-validation MSE: {cv_mse:.6f} ± {cv_std:.6f}")

    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': feature_cols,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)

    print(f"\nTop 20 Most Important Features:")
    print(feature_importance.head(20))

    # Compare with baseline
    baseline_mse = 0.0066  # From previous preprocessing
    improvement = (baseline_mse - val_mse) / baseline_mse * 100

    print(f"\nPerformance Comparison:")
    print(f"Baseline MSE: {baseline_mse:.6f}")
    print(f"Advanced MSE: {val_mse:.6f}")
    print(f"Improvement: {improvement:.2f}%")

    if improvement > 0:
        print("🎉 Advanced features improved performance!")
    else:
        print("⚠️ Advanced features did not improve performance")

    return rf, feature_importance, val_mse_orig

def main():
    """Main function for complete advanced feature engineering pipeline"""
    print("BTK DATATHON 2025 - ADVANCED FEATURE ENGINEERING PIPELINE")
    print("=" * 70)

    # Load data
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')

    # Initialize feature engineer
    feature_engineer = AdvancedFeatureEngineer()

    # Apply advanced feature engineering
    train_advanced, test_advanced = feature_engineer.apply_advanced_feature_engineering(
        train_df, test_df
    )

    # Integrate with baseline preprocessing
    X_train, y_train, X_test, train_ids, test_ids, preprocessor, feature_cols = integrate_with_baseline_preprocessing(
        train_advanced, test_advanced
    )

    # Evaluate advanced features
    model, feature_importance, final_mse = evaluate_advanced_features(
        X_train, y_train, feature_cols, preprocessor
    )

    # Save results
    print(f"\nSaving advanced feature engineering results...")

    # Save processed data
    train_final = X_train.copy()
    train_final['session_value'] = y_train
    train_final['user_session'] = train_ids
    train_final.to_csv('advanced_train_processed.csv', index=False)

    test_final = X_test.copy()
    test_final['user_session'] = test_ids
    test_final.to_csv('advanced_test_processed.csv', index=False)

    # Save feature importance
    feature_importance.to_csv('advanced_feature_importance.csv', index=False)

    # Save feature names
    pd.Series(feature_cols).to_csv('advanced_feature_names.csv', index=False)

    print(f"Advanced feature engineering complete!")
    print(f"Final MSE: {final_mse:.2f}")
    print(f"Total features: {len(feature_cols)}")

    return {
        'feature_engineer': feature_engineer,
        'preprocessor': preprocessor,
        'model': model,
        'X_train': X_train,
        'y_train': y_train,
        'X_test': X_test,
        'feature_importance': feature_importance,
        'final_mse': final_mse
    }

if __name__ == "__main__":
    results = main()
