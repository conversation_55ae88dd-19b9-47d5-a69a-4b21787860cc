#!/usr/bin/env python3
"""
Advanced Machine Learning Models for BTK Datathon 2025
XGBoost, LightGBM, CatBoost, Neural Networks with comprehensive evaluation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Advanced model imports
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost not available. Installing...")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM not available. Installing...")

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("CatBoost not available. Installing...")

try:
    from sklearn.neural_network import MLPRegressor
    import tensorflow as tf
    from tensorflow import keras
    NEURAL_NETWORKS_AVAILABLE = True
except ImportError:
    NEURAL_NETWORKS_AVAILABLE = False
    print("Neural network libraries not fully available.")

class AdvancedModelEvaluator:
    """
    Comprehensive evaluation of advanced ML models
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.models = {}
        self.results = {}
        self.best_params = {}
        
    def install_missing_packages(self):
        """Install missing packages if needed"""
        import subprocess
        import sys

        # Check what's available
        xgb_available = XGBOOST_AVAILABLE
        lgb_available = LIGHTGBM_AVAILABLE
        cb_available = CATBOOST_AVAILABLE

        print(f"Package availability: XGBoost={xgb_available}, LightGBM={lgb_available}, CatBoost={cb_available}")

        # All packages are already installed based on pip output
        return True
    
    def load_preprocessed_data(self):
        """Load the best preprocessed data available"""
        print("Loading preprocessed data...")
        
        # Try to load enhanced features first
        try:
            train_df = pd.read_csv('advanced_train_processed.csv')
            test_df = pd.read_csv('advanced_test_processed.csv')
            print(f"Loaded enhanced features: Train {train_df.shape}, Test {test_df.shape}")
            data_type = "enhanced"
        except FileNotFoundError:
            try:
                train_df = pd.read_csv('preprocessed_data/train_preprocessed.csv')
                test_df = pd.read_csv('preprocessed_data/test_preprocessed.csv')
                print(f"Loaded advanced preprocessing: Train {train_df.shape}, Test {test_df.shape}")
                data_type = "advanced"
            except FileNotFoundError:
                print("No preprocessed data found. Running preprocessing...")
                from data_preprocessing import main as preprocess_main
                data = preprocess_main()
                
                train_df = data['X_train'].copy()
                train_df['session_value'] = data['y_train']
                train_df['user_session'] = data['train_ids']
                
                test_df = data['X_test'].copy()
                test_df['user_session'] = data['test_ids']
                data_type = "runtime"
        
        return train_df, test_df, data_type
    
    def prepare_data(self, train_df, test_df):
        """Prepare data for advanced modeling"""
        print("Preparing data for advanced models...")
        
        # Identify feature columns
        exclude_cols = ['session_value', 'user_session', 'session_value_original']
        feature_cols = [col for col in train_df.columns if col not in exclude_cols]
        
        # Prepare training data
        X = train_df[feature_cols]
        y = train_df['session_value']
        
        # Prepare test data
        X_test = test_df[feature_cols]
        test_sessions = test_df['user_session']
        
        # Create validation split
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=self.random_state
        )
        
        print(f"Data prepared:")
        print(f"  Features: {len(feature_cols)}")
        print(f"  Training: {X_train.shape}")
        print(f"  Validation: {X_val.shape}")
        print(f"  Test: {X_test.shape}")
        
        return {
            'X_train': X_train, 'X_val': X_val, 'y_train': y_train, 'y_val': y_val,
            'X_test': X_test, 'test_sessions': test_sessions, 'feature_cols': feature_cols
        }
    
    def initialize_advanced_models(self):
        """Initialize advanced models with reasonable default parameters"""
        print("Initializing advanced models...")
        
        models = {}
        
        # Ensemble models (always available)
        models['Random Forest'] = RandomForestRegressor(
            n_estimators=200,
            max_depth=15,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=self.random_state,
            n_jobs=-1
        )
        
        models['Gradient Boosting'] = GradientBoostingRegressor(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            random_state=self.random_state
        )
        
        # XGBoost
        if XGBOOST_AVAILABLE:
            models['XGBoost'] = xgb.XGBRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=self.random_state,
                n_jobs=-1
            )
        
        # LightGBM
        if LIGHTGBM_AVAILABLE:
            models['LightGBM'] = lgb.LGBMRegressor(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=self.random_state,
                n_jobs=-1,
                verbose=-1
            )
        
        # CatBoost
        if CATBOOST_AVAILABLE:
            models['CatBoost'] = cb.CatBoostRegressor(
                iterations=200,
                depth=8,
                learning_rate=0.1,
                random_seed=self.random_state,
                verbose=False
            )
        
        # Neural Network
        if NEURAL_NETWORKS_AVAILABLE:
            models['Neural Network'] = MLPRegressor(
                hidden_layer_sizes=(128, 64, 32),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                max_iter=500,
                random_state=self.random_state
            )
        
        self.models = models
        print(f"Initialized {len(models)} advanced models: {list(models.keys())}")
        return models
    
    def evaluate_model(self, name, model, data, scale_features=False):
        """Evaluate a single advanced model"""
        print(f"\n🔄 Evaluating {name}...")
        
        X_train, X_val = data['X_train'], data['X_val']
        y_train, y_val = data['y_train'], data['y_val']
        
        # Scale features if needed (for neural networks)
        if scale_features:
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            X_train_model, X_val_model = X_train_scaled, X_val_scaled
        else:
            X_train_model, X_val_model = X_train, X_val
        
        try:
            # Train model
            if 'Neural Network' in name:
                # Special handling for neural networks
                model.fit(X_train_model, y_train)
            else:
                model.fit(X_train_model, y_train)
            
            # Predictions
            y_train_pred = model.predict(X_train_model)
            y_val_pred = model.predict(X_val_model)
            
            # Calculate metrics
            train_mse = mean_squared_error(y_train, y_train_pred)
            val_mse = mean_squared_error(y_val, y_val_pred)
            train_mae = mean_absolute_error(y_train, y_train_pred)
            val_mae = mean_absolute_error(y_val, y_val_pred)
            train_r2 = r2_score(y_train, y_train_pred)
            val_r2 = r2_score(y_val, y_val_pred)
            
            # Cross-validation
            if scale_features:
                X_full_scaled = scaler.fit_transform(data['X_train'].append(data['X_val']))
                y_full = data['y_train'].append(data['y_val'])
                cv_scores = cross_val_score(model, X_full_scaled, y_full, cv=5, 
                                          scoring='neg_mean_squared_error', n_jobs=-1)
            else:
                X_full = pd.concat([data['X_train'], data['X_val']])
                y_full = pd.concat([data['y_train'], data['y_val']])
                cv_scores = cross_val_score(model, X_full, y_full, cv=5, 
                                          scoring='neg_mean_squared_error', n_jobs=-1)
            
            cv_mse = -cv_scores.mean()
            cv_std = cv_scores.std()
            
            # Store results
            results = {
                'model': model,
                'scaler': scaler if scale_features else None,
                'train_mse': train_mse,
                'val_mse': val_mse,
                'train_mae': train_mae,
                'val_mae': val_mae,
                'train_r2': train_r2,
                'val_r2': val_r2,
                'cv_mse': cv_mse,
                'cv_std': cv_std,
                'overfitting': val_mse - train_mse,
                'scaled': scale_features
            }
            
            # Print results
            print(f"  🎯 Validation MSE: {val_mse:.6f}")
            print(f"  📊 Validation R²: {val_r2:.6f}")
            print(f"  🔄 CV MSE: {cv_mse:.6f} ± {cv_std:.6f}")
            print(f"  📈 Overfitting: {val_mse - train_mse:.6f}")
            
            return results
            
        except Exception as e:
            print(f"  ❌ Failed to evaluate {name}: {e}")
            return None
    
    def evaluate_all_models(self, data):
        """Evaluate all available advanced models"""
        print("\n" + "="*60)
        print("ADVANCED MODEL EVALUATION")
        print("="*60)
        
        # Install missing packages
        self.install_missing_packages()
        
        # Re-initialize models after potential installation
        self.initialize_advanced_models()
        
        # Evaluate each model
        for name, model in self.models.items():
            # Determine if scaling is needed
            scale_features = 'Neural Network' in name
            
            result = self.evaluate_model(name, model, data, scale_features)
            if result is not None:
                self.results[name] = result
        
        return self.results
    
    def hyperparameter_tuning(self, data, top_models=3):
        """Perform hyperparameter tuning for top models"""
        print("\n" + "="*60)
        print("HYPERPARAMETER TUNING")
        print("="*60)
        
        # Sort models by validation MSE
        sorted_models = sorted(self.results.items(), key=lambda x: x[1]['val_mse'])
        top_model_names = [name for name, _ in sorted_models[:top_models]]
        
        print(f"Tuning top {top_models} models: {top_model_names}")
        
        # Define parameter grids
        param_grids = {
            'Random Forest': {
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 15, 20],
                'min_samples_split': [5, 10, 20]
            },
            'XGBoost': {
                'n_estimators': [100, 200, 300],
                'max_depth': [6, 8, 10],
                'learning_rate': [0.05, 0.1, 0.15],
                'subsample': [0.8, 0.9, 1.0]
            },
            'LightGBM': {
                'n_estimators': [100, 200, 300],
                'max_depth': [6, 8, 10],
                'learning_rate': [0.05, 0.1, 0.15],
                'subsample': [0.8, 0.9, 1.0]
            },
            'CatBoost': {
                'iterations': [100, 200, 300],
                'depth': [6, 8, 10],
                'learning_rate': [0.05, 0.1, 0.15]
            }
        }
        
        tuned_results = {}
        
        for name in top_model_names:
            if name not in param_grids:
                print(f"No parameter grid defined for {name}, skipping...")
                continue
            
            print(f"\nTuning {name}...")
            
            # Get base model
            if name == 'Random Forest':
                base_model = RandomForestRegressor(random_state=self.random_state, n_jobs=-1)
            elif name == 'XGBoost' and XGBOOST_AVAILABLE:
                base_model = xgb.XGBRegressor(random_state=self.random_state, n_jobs=-1)
            elif name == 'LightGBM' and LIGHTGBM_AVAILABLE:
                base_model = lgb.LGBMRegressor(random_state=self.random_state, n_jobs=-1, verbose=-1)
            elif name == 'CatBoost' and CATBOOST_AVAILABLE:
                base_model = cb.CatBoostRegressor(random_seed=self.random_state, verbose=False)
            else:
                continue
            
            # Perform grid search
            X_full = pd.concat([data['X_train'], data['X_val']])
            y_full = pd.concat([data['y_train'], data['y_val']])
            
            grid_search = GridSearchCV(
                base_model,
                param_grids[name],
                cv=3,  # Reduced for speed
                scoring='neg_mean_squared_error',
                n_jobs=-1,
                verbose=1
            )
            
            try:
                grid_search.fit(X_full, y_full)
                
                # Store best parameters and score
                tuned_results[name] = {
                    'best_params': grid_search.best_params_,
                    'best_score': -grid_search.best_score_,
                    'best_model': grid_search.best_estimator_
                }
                
                print(f"  Best parameters: {grid_search.best_params_}")
                print(f"  Best CV MSE: {-grid_search.best_score_:.6f}")
                
            except Exception as e:
                print(f"  Failed to tune {name}: {e}")
        
        return tuned_results

def main():
    """Main advanced model evaluation pipeline"""
    print("BTK DATATHON 2025 - ADVANCED MODEL EVALUATION")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = AdvancedModelEvaluator()
    
    # Load data
    train_df, test_df, data_type = evaluator.load_preprocessed_data()
    
    # Prepare data
    data = evaluator.prepare_data(train_df, test_df)
    
    # Evaluate all models
    results = evaluator.evaluate_all_models(data)
    
    if not results:
        print("No models were successfully evaluated!")
        return None
    
    return {
        'evaluator': evaluator,
        'results': results,
        'data': data,
        'data_type': data_type
    }

if __name__ == "__main__":
    advanced_results = main()
