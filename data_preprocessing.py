#!/usr/bin/env python3
"""
Data Preprocessing Pipeline for BTK Datathon 2025
E-commerce Session Value Prediction
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler, LabelEncoder, TargetEncoder
from sklearn.model_selection import train_test_split
from sklearn.ensemble import IsolationForest
import warnings
warnings.filterwarnings('ignore')

class SessionDataPreprocessor:
    """
    Comprehensive preprocessing pipeline for session value prediction
    """
    
    def __init__(self, target_col='session_value', random_state=42):
        self.target_col = target_col
        self.random_state = random_state
        self.scalers = {}
        self.encoders = {}
        self.feature_columns = []
        self.outlier_detector = None
        self.session_stats = {}
        
    def load_data(self, train_path='train.csv', test_path='test.csv'):
        """Load and basic preparation of data"""
        print("Loading data...")
        
        self.train_df = pd.read_csv(train_path)
        self.test_df = pd.read_csv(test_path)
        
        # Convert datetime
        self.train_df['event_time'] = pd.to_datetime(self.train_df['event_time'])
        self.test_df['event_time'] = pd.to_datetime(self.test_df['event_time'])
        
        print(f"Training data: {self.train_df.shape}")
        print(f"Test data: {self.test_df.shape}")
        
        return self.train_df, self.test_df
    
    def create_session_features(self, df, is_train=True):
        """Create session-level features from event-level data"""
        print(f"Creating session features for {'training' if is_train else 'test'} data...")
        
        # Basic session aggregations
        session_agg = df.groupby('user_session').agg({
            'event_time': ['min', 'max', 'count'],
            'product_id': 'nunique',
            'category_id': 'nunique',
            'user_id': 'first'
        })
        
        # Flatten column names
        session_agg.columns = ['first_event_time', 'last_event_time', 'total_events',
                              'unique_products', 'unique_categories', 'user_id']
        session_agg = session_agg.reset_index()
        
        # Session duration
        session_agg['session_duration_minutes'] = (
            session_agg['last_event_time'] - session_agg['first_event_time']
        ).dt.total_seconds() / 60
        
        # Handle single-event sessions (duration = 0)
        session_agg['session_duration_minutes'] = session_agg['session_duration_minutes'].fillna(0)
        
        # Event type counts
        event_counts = df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
        event_counts = event_counts.reset_index()
        
        # Merge event counts
        session_features = session_agg.merge(event_counts, on='user_session', how='left')
        
        # Fill missing event types with 0
        for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
            if event_type not in session_features.columns:
                session_features[event_type] = 0
            session_features[event_type] = session_features[event_type].fillna(0)
        
        # Rename event columns
        session_features = session_features.rename(columns={
            'VIEW': 'view_count',
            'ADD_CART': 'add_cart_count', 
            'REMOVE_CART': 'remove_cart_count',
            'BUY': 'buy_count'
        })
        
        # Behavioral ratios (avoid division by zero)
        session_features['view_to_cart_ratio'] = np.where(
            session_features['view_count'] > 0,
            session_features['add_cart_count'] / session_features['view_count'],
            0
        )
        
        session_features['cart_to_buy_ratio'] = np.where(
            session_features['add_cart_count'] > 0,
            session_features['buy_count'] / session_features['add_cart_count'],
            0
        )
        
        session_features['remove_to_cart_ratio'] = np.where(
            session_features['add_cart_count'] > 0,
            session_features['remove_cart_count'] / session_features['add_cart_count'],
            0
        )
        
        # Events per minute (engagement rate)
        session_features['events_per_minute'] = np.where(
            session_features['session_duration_minutes'] > 0,
            session_features['total_events'] / session_features['session_duration_minutes'],
            session_features['total_events']  # For single-event sessions
        )
        
        # Temporal features
        session_features['hour'] = session_features['first_event_time'].dt.hour
        session_features['day_of_week'] = session_features['first_event_time'].dt.dayofweek
        session_features['is_weekend'] = session_features['day_of_week'].isin([5, 6]).astype(int)
        session_features['day_of_month'] = session_features['first_event_time'].dt.day
        
        # Add target variable for training data
        if is_train and self.target_col in df.columns:
            target_values = df.groupby('user_session')[self.target_col].first()
            session_features = session_features.merge(
                target_values.reset_index(), on='user_session', how='left'
            )
        
        # Drop datetime columns
        session_features = session_features.drop(['first_event_time', 'last_event_time'], axis=1)
        
        return session_features
    
    def create_user_features(self, session_df, is_train=True):
        """Create user-level aggregated features"""
        print(f"Creating user features for {'training' if is_train else 'test'} data...")
        
        # User-level aggregations
        user_agg = session_df.groupby('user_id').agg({
            'total_events': ['mean', 'sum', 'std'],
            'unique_products': ['mean', 'sum'],
            'unique_categories': ['mean', 'sum'],
            'session_duration_minutes': ['mean', 'sum'],
            'buy_count': 'sum',
            'view_count': 'sum',
            'add_cart_count': 'sum',
            'user_session': 'count'  # Number of sessions per user
        })
        
        # Flatten column names
        user_agg.columns = [
            'user_avg_events', 'user_total_events', 'user_events_std',
            'user_avg_products', 'user_total_products',
            'user_avg_categories', 'user_total_categories',
            'user_avg_duration', 'user_total_duration',
            'user_total_buys', 'user_total_views', 'user_total_carts',
            'user_session_count'
        ]
        user_agg = user_agg.reset_index()
        
        # Fill NaN std values with 0 (single session users)
        user_agg['user_events_std'] = user_agg['user_events_std'].fillna(0)
        
        # User conversion rates
        user_agg['user_conversion_rate'] = np.where(
            user_agg['user_total_views'] > 0,
            user_agg['user_total_buys'] / user_agg['user_total_views'],
            0
        )
        
        user_agg['user_cart_conversion_rate'] = np.where(
            user_agg['user_total_carts'] > 0,
            user_agg['user_total_buys'] / user_agg['user_total_carts'],
            0
        )
        
        # Add target-based user features for training
        if is_train and self.target_col in session_df.columns:
            user_target_agg = session_df.groupby('user_id')[self.target_col].agg(['mean', 'std', 'sum'])
            user_target_agg.columns = ['user_avg_session_value', 'user_session_value_std', 'user_total_value']
            user_target_agg['user_session_value_std'] = user_target_agg['user_session_value_std'].fillna(0)
            user_agg = user_agg.merge(user_target_agg.reset_index(), on='user_id', how='left')
        
        return user_agg
    
    def handle_missing_values(self, df):
        """Handle missing values with appropriate strategies"""
        print("Handling missing values...")
        
        missing_info = df.isnull().sum()
        if missing_info.sum() > 0:
            print("Missing values found:")
            print(missing_info[missing_info > 0])
            
            # Fill numerical columns with median
            numerical_cols = df.select_dtypes(include=[np.number]).columns
            for col in numerical_cols:
                if df[col].isnull().sum() > 0:
                    median_val = df[col].median()
                    df[col] = df[col].fillna(median_val)
                    print(f"Filled {col} with median: {median_val}")
            
            # Fill categorical columns with mode
            categorical_cols = df.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                if df[col].isnull().sum() > 0:
                    mode_val = df[col].mode()[0] if len(df[col].mode()) > 0 else 'Unknown'
                    df[col] = df[col].fillna(mode_val)
                    print(f"Filled {col} with mode: {mode_val}")
        else:
            print("No missing values found!")
        
        return df
    
    def detect_and_handle_outliers(self, df, target_col=None, contamination=0.05):
        """Detect and handle outliers using Isolation Forest"""
        print("Detecting and handling outliers...")
        
        if target_col and target_col in df.columns:
            # Focus on target variable outliers
            target_values = df[target_col].values.reshape(-1, 1)
            
            # Use IQR method for target variable
            Q1 = df[target_col].quantile(0.25)
            Q3 = df[target_col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers_mask = (df[target_col] < lower_bound) | (df[target_col] > upper_bound)
            n_outliers = outliers_mask.sum()
            
            print(f"Target variable outliers detected: {n_outliers} ({n_outliers/len(df)*100:.2f}%)")
            print(f"Outlier bounds: [{lower_bound:.2f}, {upper_bound:.2f}]")
            
            # Store outlier information
            self.outlier_info = {
                'lower_bound': lower_bound,
                'upper_bound': upper_bound,
                'n_outliers': n_outliers,
                'outlier_indices': df[outliers_mask].index.tolist()
            }
            
            # Option 1: Cap outliers (recommended for this dataset)
            df[target_col] = df[target_col].clip(lower=lower_bound, upper=upper_bound)
            print("Outliers capped to bounds")
            
        return df
    
    def apply_target_transformation(self, df, target_col):
        """Apply log transformation to target variable"""
        print("Applying target transformation...")
        
        if target_col in df.columns:
            original_target = f"{target_col}_original"
            df[original_target] = df[target_col].copy()
            
            # Log transformation (log1p to handle zeros)
            df[target_col] = np.log1p(df[target_col])
            
            print(f"Applied log1p transformation to {target_col}")
            print(f"Original range: [{df[original_target].min():.2f}, {df[original_target].max():.2f}]")
            print(f"Transformed range: [{df[target_col].min():.4f}, {df[target_col].max():.4f}]")
            
            # Store transformation info
            self.target_transform_info = {
                'method': 'log1p',
                'original_min': df[original_target].min(),
                'original_max': df[original_target].max(),
                'transformed_min': df[target_col].min(),
                'transformed_max': df[target_col].max()
            }
        
        return df
    
    def encode_categorical_features(self, train_df, test_df=None):
        """Encode categorical features using appropriate methods"""
        print("Encoding categorical features...")
        
        # Identify categorical columns (excluding target)
        categorical_cols = ['user_id']  # High cardinality - use target encoding
        
        encoded_dfs = []
        
        for df_name, df in [('train', train_df), ('test', test_df)]:
            if df is None:
                encoded_dfs.append(None)
                continue
                
            df_encoded = df.copy()
            
            # Target encoding for high cardinality features
            for col in categorical_cols:
                if col in df_encoded.columns:
                    if df_name == 'train':
                        # Fit target encoder on training data
                        encoder = TargetEncoder(random_state=self.random_state)
                        if self.target_col in df_encoded.columns:
                            df_encoded[f'{col}_encoded'] = encoder.fit_transform(
                                df_encoded[[col]], df_encoded[self.target_col]
                            )
                            self.encoders[col] = encoder
                        else:
                            # For test data without target, use frequency encoding
                            freq_encoding = df_encoded[col].value_counts().to_dict()
                            df_encoded[f'{col}_encoded'] = df_encoded[col].map(freq_encoding)
                    else:
                        # Apply fitted encoder to test data
                        if col in self.encoders:
                            df_encoded[f'{col}_encoded'] = self.encoders[col].transform(df_encoded[[col]])
                        else:
                            # Fallback to frequency encoding
                            freq_encoding = train_df[col].value_counts().to_dict()
                            df_encoded[f'{col}_encoded'] = df_encoded[col].map(freq_encoding).fillna(0)
                    
                    # Drop original categorical column
                    df_encoded = df_encoded.drop(col, axis=1)
            
            encoded_dfs.append(df_encoded)
        
        return encoded_dfs[0], encoded_dfs[1]
    
    def scale_numerical_features(self, train_df, test_df=None, method='robust'):
        """Scale numerical features"""
        print(f"Scaling numerical features using {method} scaler...")

        # Identify numerical columns (excluding target and identifiers)
        exclude_cols = [self.target_col, 'user_session', f'{self.target_col}_original']
        train_numerical_cols = [col for col in train_df.select_dtypes(include=[np.number]).columns
                               if col not in exclude_cols]

        # For test data, only use columns that exist in both datasets
        if test_df is not None:
            test_numerical_cols = [col for col in test_df.select_dtypes(include=[np.number]).columns
                                  if col not in exclude_cols]
            # Use intersection of columns
            common_numerical_cols = list(set(train_numerical_cols) & set(test_numerical_cols))
        else:
            common_numerical_cols = train_numerical_cols

        print(f"Scaling columns: {common_numerical_cols}")

        # Choose scaler
        if method == 'robust':
            scaler = RobustScaler()
        else:
            scaler = StandardScaler()

        scaled_dfs = []

        for df_name, df in [('train', train_df), ('test', test_df)]:
            if df is None:
                scaled_dfs.append(None)
                continue

            df_scaled = df.copy()

            # Get columns that exist in current dataframe
            existing_cols = [col for col in common_numerical_cols if col in df_scaled.columns]

            if df_name == 'train':
                # Fit and transform training data
                if existing_cols:
                    df_scaled[existing_cols] = scaler.fit_transform(df_scaled[existing_cols])
                    self.scalers['numerical'] = scaler
                    self.scalers['numerical_columns'] = existing_cols
            else:
                # Transform test data using fitted scaler
                if existing_cols and 'numerical' in self.scalers:
                    # Only scale columns that were scaled in training
                    scale_cols = [col for col in existing_cols if col in self.scalers['numerical_columns']]
                    if scale_cols:
                        df_scaled[scale_cols] = self.scalers['numerical'].transform(df_scaled[scale_cols])

            scaled_dfs.append(df_scaled)

        return scaled_dfs[0], scaled_dfs[1]
    
    def remove_unnecessary_columns(self, df):
        """Remove columns not needed for modeling"""
        print("Removing unnecessary columns...")
        
        # Columns to remove
        cols_to_remove = ['user_session']  # Keep as identifier but not feature
        
        # Keep user_session for final submission but mark as non-feature
        feature_df = df.copy()
        
        # Identify final feature columns
        exclude_cols = [self.target_col, f'{self.target_col}_original', 'user_session']
        self.feature_columns = [col for col in feature_df.columns if col not in exclude_cols]
        
        print(f"Final feature columns ({len(self.feature_columns)}): {self.feature_columns}")
        
        return feature_df
    
    def get_preprocessing_summary(self):
        """Get summary of preprocessing steps"""
        summary = {
            'feature_columns': self.feature_columns,
            'n_features': len(self.feature_columns),
            'scalers': list(self.scalers.keys()),
            'encoders': list(self.encoders.keys()),
            'target_transform': getattr(self, 'target_transform_info', None),
            'outlier_info': getattr(self, 'outlier_info', None)
        }
        return summary

    def fit_transform(self, train_path='train.csv', test_path='test.csv'):
        """Complete preprocessing pipeline"""
        print("STARTING COMPLETE PREPROCESSING PIPELINE")
        print("=" * 60)

        # Step 1: Load data
        train_df, test_df = self.load_data(train_path, test_path)

        # Step 2: Create session-level features
        train_sessions = self.create_session_features(train_df, is_train=True)
        test_sessions = self.create_session_features(test_df, is_train=False)

        # Step 3: Create user-level features
        train_user_features = self.create_user_features(train_sessions, is_train=True)
        test_user_features = self.create_user_features(test_sessions, is_train=False)

        # Step 4: Merge user features back to session features
        train_sessions = train_sessions.merge(train_user_features, on='user_id', how='left')
        test_sessions = test_sessions.merge(test_user_features, on='user_id', how='left')

        # Step 5: Handle missing values
        train_sessions = self.handle_missing_values(train_sessions)
        test_sessions = self.handle_missing_values(test_sessions)

        # Step 6: Handle outliers (only on training data)
        train_sessions = self.detect_and_handle_outliers(train_sessions, self.target_col)

        # Step 7: Apply target transformation
        train_sessions = self.apply_target_transformation(train_sessions, self.target_col)

        # Step 8: Encode categorical features
        train_sessions, test_sessions = self.encode_categorical_features(train_sessions, test_sessions)

        # Step 9: Scale numerical features
        train_sessions, test_sessions = self.scale_numerical_features(train_sessions, test_sessions)

        # Step 10: Remove unnecessary columns and finalize
        train_final = self.remove_unnecessary_columns(train_sessions)
        test_final = self.remove_unnecessary_columns(test_sessions)

        # Step 11: Prepare final datasets
        X_train = train_final[self.feature_columns]
        y_train = train_final[self.target_col]
        X_test = test_final[self.feature_columns]

        # Store session identifiers for submission
        train_sessions_ids = train_final['user_session']
        test_sessions_ids = test_final['user_session']

        print("\n" + "=" * 60)
        print("PREPROCESSING COMPLETE!")
        print("=" * 60)
        print(f"Training features shape: {X_train.shape}")
        print(f"Training target shape: {y_train.shape}")
        print(f"Test features shape: {X_test.shape}")

        # Print preprocessing summary
        summary = self.get_preprocessing_summary()
        print(f"\nPreprocessing Summary:")
        print(f"- Number of features: {summary['n_features']}")
        print(f"- Scalers applied: {summary['scalers']}")
        print(f"- Encoders applied: {summary['encoders']}")
        if summary['target_transform']:
            print(f"- Target transformation: {summary['target_transform']['method']}")
        if summary['outlier_info']:
            print(f"- Outliers handled: {summary['outlier_info']['n_outliers']} capped")

        return X_train, y_train, X_test, train_sessions_ids, test_sessions_ids

    def inverse_transform_target(self, y_pred):
        """Inverse transform predictions back to original scale"""
        if hasattr(self, 'target_transform_info'):
            if self.target_transform_info['method'] == 'log1p':
                return np.expm1(y_pred)
        return y_pred

    def save_preprocessed_data(self, X_train, y_train, X_test, train_ids, test_ids,
                              output_dir='preprocessed_data'):
        """Save preprocessed data for later use"""
        import os
        os.makedirs(output_dir, exist_ok=True)

        # Save training data
        train_data = X_train.copy()
        train_data[self.target_col] = y_train
        train_data['user_session'] = train_ids
        train_data.to_csv(f'{output_dir}/train_preprocessed.csv', index=False)

        # Save test data
        test_data = X_test.copy()
        test_data['user_session'] = test_ids
        test_data.to_csv(f'{output_dir}/test_preprocessed.csv', index=False)

        # Save feature names
        pd.Series(self.feature_columns).to_csv(f'{output_dir}/feature_names.csv', index=False)

        print(f"Preprocessed data saved to {output_dir}/")

def create_validation_split(X_train, y_train, train_ids, test_size=0.2, random_state=42):
    """Create validation split with proper session grouping"""
    print("Creating validation split...")

    # Group by user sessions to prevent data leakage
    unique_sessions = train_ids.unique()
    train_sessions, val_sessions = train_test_split(
        unique_sessions, test_size=test_size, random_state=random_state
    )

    # Create masks
    train_mask = train_ids.isin(train_sessions)
    val_mask = train_ids.isin(val_sessions)

    # Split data
    X_train_split = X_train[train_mask]
    y_train_split = y_train[train_mask]
    X_val_split = X_train[val_mask]
    y_val_split = y_train[val_mask]

    print(f"Training split: {X_train_split.shape}")
    print(f"Validation split: {X_val_split.shape}")

    return X_train_split, X_val_split, y_train_split, y_val_split

def print_best_practices():
    """Print best practices for this dataset"""
    print("\n" + "=" * 60)
    print("BEST PRACTICES FOR THIS DATASET")
    print("=" * 60)

    practices = [
        "1. SESSION-LEVEL MODELING: Always aggregate to session level first",
        "2. TARGET TRANSFORMATION: Use log1p due to high skewness (8.73)",
        "3. OUTLIER HANDLING: Cap extreme values rather than removing (preserves data)",
        "4. FEATURE ENCODING: Use target encoding for high-cardinality features",
        "5. SCALING: Use RobustScaler due to outliers and skewed distributions",
        "6. VALIDATION: Use session-based splits to prevent data leakage",
        "7. EVALUATION: Use MSE on transformed target, convert back for interpretation",
        "8. FEATURE IMPORTANCE: Focus on behavioral features (buy_count, ratios)",
        "9. TEMPORAL FEATURES: Include but expect lower importance than behavioral",
        "10. USER FEATURES: Aggregate user history for better predictions"
    ]

    for practice in practices:
        print(f"✅ {practice}")

    print("\n🎯 EXPECTED PERFORMANCE:")
    print("- Baseline (session features only): MSE ~500-600")
    print("- With user features: MSE ~400-500")
    print("- With advanced engineering: MSE ~300-400")
    print("- Target: MSE <300 with ensemble methods")

def main():
    """Main preprocessing pipeline"""
    print("BTK DATATHON 2025 - DATA PREPROCESSING PIPELINE")
    print("=" * 60)

    # Initialize preprocessor
    preprocessor = SessionDataPreprocessor()

    # Run complete preprocessing
    X_train, y_train, X_test, train_ids, test_ids = preprocessor.fit_transform()

    # Create validation split
    X_train_split, X_val_split, y_train_split, y_val_split = create_validation_split(
        X_train, y_train, train_ids
    )

    # Save preprocessed data
    preprocessor.save_preprocessed_data(X_train, y_train, X_test, train_ids, test_ids)

    # Print best practices
    print_best_practices()

    return {
        'preprocessor': preprocessor,
        'X_train': X_train, 'y_train': y_train,
        'X_val': X_val_split, 'y_val': y_val_split,
        'X_test': X_test,
        'train_ids': train_ids, 'test_ids': test_ids
    }

if __name__ == "__main__":
    data = main()
