#!/usr/bin/env python3
"""
Baseline Machine Learning Models for BTK Datathon 2025
Simple algorithms with comprehensive evaluation using MSE metric
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearReg<PERSON>, <PERSON>, <PERSON>so
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.neighbors import KNeighborsRegressor
from sklearn.svm import SVR
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class BaselineModelEvaluator:
    """
    Comprehensive baseline model evaluation with MSE focus
    """
    
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.models = {}
        self.results = {}
        self.scaler = StandardScaler()
        
    def load_preprocessed_data(self):
        """Load preprocessed data for modeling"""
        print("Loading preprocessed data...")
        
        try:
            # Try to load from preprocessed data
            train_df = pd.read_csv('preprocessed_data/train_preprocessed.csv')
            test_df = pd.read_csv('preprocessed_data/test_preprocessed.csv')
            feature_names = pd.read_csv('preprocessed_data/feature_names.csv').iloc[:, 0].tolist()
            
            print(f"Loaded preprocessed data: Train {train_df.shape}, Test {test_df.shape}")
            
        except FileNotFoundError:
            print("Preprocessed data not found. Running preprocessing pipeline...")
            from data_preprocessing import main as preprocess_main
            data = preprocess_main()
            
            # Create dataframes
            train_df = data['X_train'].copy()
            train_df['session_value'] = data['y_train']
            train_df['user_session'] = data['train_ids']
            
            test_df = data['X_test'].copy()
            test_df['user_session'] = data['test_ids']
            
            feature_names = data['preprocessor'].feature_columns
        
        return train_df, test_df, feature_names
    
    def prepare_data_for_modeling(self, train_df, test_df, feature_names, target_col='session_value'):
        """Prepare data for baseline modeling"""
        print("Preparing data for modeling...")
        
        # Separate features and target
        X_train = train_df[feature_names]
        y_train = train_df[target_col]
        X_test = test_df[feature_names]
        
        # Get session IDs for submission
        train_sessions = train_df['user_session']
        test_sessions = test_df['user_session']
        
        # Create validation split
        X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
            X_train, y_train, test_size=0.2, random_state=self.random_state
        )
        
        print(f"Data prepared:")
        print(f"  Training: {X_train_split.shape}")
        print(f"  Validation: {X_val_split.shape}")
        print(f"  Test: {X_test.shape}")
        print(f"  Features: {len(feature_names)}")
        
        return {
            'X_train': X_train_split,
            'X_val': X_val_split,
            'y_train': y_train_split,
            'y_val': y_val_split,
            'X_test': X_test,
            'train_sessions': train_sessions,
            'test_sessions': test_sessions,
            'feature_names': feature_names
        }
    
    def initialize_baseline_models(self):
        """Initialize simple baseline models"""
        print("Initializing baseline models...")
        
        self.models = {
            # Linear Models
            'Linear Regression': LinearRegression(),
            'Ridge Regression': Ridge(alpha=1.0, random_state=self.random_state),
            'Lasso Regression': Lasso(alpha=0.1, random_state=self.random_state),
            
            # Tree-based Models
            'Decision Tree': DecisionTreeRegressor(
                max_depth=10, 
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=self.random_state
            ),
            'Random Forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_split=20,
                random_state=self.random_state,
                n_jobs=-1
            ),
            
            # Instance-based Models
            'K-Nearest Neighbors': KNeighborsRegressor(n_neighbors=5),
            
            # Support Vector Machine (simplified for baseline)
            'Support Vector Regression': SVR(kernel='rbf', C=1.0, gamma='scale')
        }
        
        print(f"Initialized {len(self.models)} baseline models")
        return self.models
    
    def evaluate_single_model(self, name, model, data):
        """Evaluate a single model comprehensively"""
        print(f"\nEvaluating {name}...")
        
        X_train, X_val = data['X_train'], data['X_val']
        y_train, y_val = data['y_train'], data['y_val']
        
        # Scale data for models that need it
        if name in ['K-Nearest Neighbors', 'Support Vector Regression', 'Ridge Regression', 'Lasso Regression']:
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Train model
            model.fit(X_train_scaled, y_train)
            
            # Predictions
            y_train_pred = model.predict(X_train_scaled)
            y_val_pred = model.predict(X_val_scaled)
            
            # Store scaler for later use
            self.models[f'{name}_scaler'] = scaler
            
        else:
            # Train model without scaling
            model.fit(X_train, y_train)
            
            # Predictions
            y_train_pred = model.predict(X_train)
            y_val_pred = model.predict(X_val)
        
        # Calculate metrics
        train_mse = mean_squared_error(y_train, y_train_pred)
        val_mse = mean_squared_error(y_val, y_val_pred)
        train_mae = mean_absolute_error(y_train, y_train_pred)
        val_mae = mean_absolute_error(y_val, y_val_pred)
        train_r2 = r2_score(y_train, y_train_pred)
        val_r2 = r2_score(y_val, y_val_pred)
        
        # Cross-validation
        try:
            if name in ['K-Nearest Neighbors', 'Support Vector Regression', 'Ridge Regression', 'Lasso Regression']:
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, 
                                          scoring='neg_mean_squared_error', n_jobs=-1)
            else:
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, 
                                          scoring='neg_mean_squared_error', n_jobs=-1)
            cv_mse = -cv_scores.mean()
            cv_std = cv_scores.std()
        except Exception as e:
            print(f"  Cross-validation failed for {name}: {e}")
            cv_mse, cv_std = val_mse, 0
        
        # Store results
        results = {
            'model': model,
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_r2': train_r2,
            'val_r2': val_r2,
            'cv_mse': cv_mse,
            'cv_std': cv_std,
            'overfitting': val_mse - train_mse
        }
        
        # Print results
        print(f"  Training MSE: {train_mse:.6f}")
        print(f"  Validation MSE: {val_mse:.6f}")
        print(f"  Validation R²: {val_r2:.6f}")
        print(f"  CV MSE: {cv_mse:.6f} ± {cv_std:.6f}")
        print(f"  Overfitting: {results['overfitting']:.6f}")
        
        return results
    
    def evaluate_all_models(self, data):
        """Evaluate all baseline models"""
        print("\n" + "="*60)
        print("BASELINE MODEL EVALUATION")
        print("="*60)

        # Create a list of model names to avoid dictionary size change during iteration
        model_names = [name for name in self.models.keys() if '_scaler' not in name]

        for name in model_names:
            model = self.models[name]
            try:
                self.results[name] = self.evaluate_single_model(name, model, data)
            except Exception as e:
                print(f"Failed to evaluate {name}: {e}")
                continue

        return self.results
    
    def create_performance_comparison(self):
        """Create comprehensive performance comparison"""
        print("\n" + "="*60)
        print("PERFORMANCE COMPARISON")
        print("="*60)
        
        # Create comparison DataFrame
        comparison_data = []
        for name, results in self.results.items():
            comparison_data.append({
                'Model': name,
                'Validation MSE': results['val_mse'],
                'Validation R²': results['val_r2'],
                'CV MSE': results['cv_mse'],
                'CV Std': results['cv_std'],
                'Overfitting': results['overfitting'],
                'Training MSE': results['train_mse']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('Validation MSE')
        
        print("Model Performance Ranking (by Validation MSE):")
        print("=" * 80)
        for i, row in comparison_df.iterrows():
            print(f"{row.name + 1:2d}. {row['Model']:<25} | "
                  f"Val MSE: {row['Validation MSE']:.6f} | "
                  f"R²: {row['Validation R²']:.6f} | "
                  f"CV: {row['CV MSE']:.6f}±{row['CV Std']:.6f}")
        
        return comparison_df
    
    def create_visualizations(self, comparison_df, data):
        """Create performance visualizations"""
        print("\nCreating performance visualizations...")
        
        # Set up the plotting style
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. MSE Comparison
        comparison_df.plot(x='Model', y='Validation MSE', kind='bar', ax=axes[0,0], 
                          color='skyblue', legend=False)
        axes[0,0].set_title('Validation MSE Comparison')
        axes[0,0].set_ylabel('MSE')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # 2. R² Comparison
        comparison_df.plot(x='Model', y='Validation R²', kind='bar', ax=axes[0,1], 
                          color='lightgreen', legend=False)
        axes[0,1].set_title('Validation R² Comparison')
        axes[0,1].set_ylabel('R²')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # 3. Overfitting Analysis
        comparison_df.plot(x='Model', y='Overfitting', kind='bar', ax=axes[0,2], 
                          color='salmon', legend=False)
        axes[0,2].set_title('Overfitting Analysis (Val MSE - Train MSE)')
        axes[0,2].set_ylabel('Overfitting Score')
        axes[0,2].tick_params(axis='x', rotation=45)
        axes[0,2].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        # 4. Cross-validation stability
        axes[1,0].errorbar(range(len(comparison_df)), comparison_df['CV MSE'], 
                          yerr=comparison_df['CV Std'], fmt='o', capsize=5)
        axes[1,0].set_xticks(range(len(comparison_df)))
        axes[1,0].set_xticklabels(comparison_df['Model'], rotation=45)
        axes[1,0].set_title('Cross-Validation MSE with Error Bars')
        axes[1,0].set_ylabel('CV MSE')
        
        # 5. Actual vs Predicted for best model
        best_model_name = comparison_df.iloc[0]['Model']
        best_model = self.results[best_model_name]['model']
        
        X_val, y_val = data['X_val'], data['y_val']
        
        # Handle scaling if needed
        if f'{best_model_name}_scaler' in self.models:
            scaler = self.models[f'{best_model_name}_scaler']
            X_val_scaled = scaler.transform(X_val)
            y_val_pred = best_model.predict(X_val_scaled)
        else:
            y_val_pred = best_model.predict(X_val)
        
        axes[1,1].scatter(y_val, y_val_pred, alpha=0.5, s=10)
        axes[1,1].plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 'r--', lw=2)
        axes[1,1].set_xlabel('Actual')
        axes[1,1].set_ylabel('Predicted')
        axes[1,1].set_title(f'Actual vs Predicted - {best_model_name}')
        
        # 6. Residuals plot for best model
        residuals = y_val - y_val_pred
        axes[1,2].scatter(y_val_pred, residuals, alpha=0.5, s=10)
        axes[1,2].axhline(y=0, color='r', linestyle='--')
        axes[1,2].set_xlabel('Predicted')
        axes[1,2].set_ylabel('Residuals')
        axes[1,2].set_title(f'Residuals Plot - {best_model_name}')
        
        plt.tight_layout()
        plt.show()
        
        return best_model_name
    
    def generate_predictions(self, data, best_model_name):
        """Generate predictions for test set using best model"""
        print(f"\nGenerating predictions using {best_model_name}...")
        
        best_model = self.results[best_model_name]['model']
        X_test = data['X_test']
        test_sessions = data['test_sessions']
        
        # Handle scaling if needed
        if f'{best_model_name}_scaler' in self.models:
            scaler = self.models[f'{best_model_name}_scaler']
            X_test_scaled = scaler.transform(X_test)
            test_predictions = best_model.predict(X_test_scaled)
        else:
            test_predictions = best_model.predict(X_test)
        
        # Create submission DataFrame
        submission_df = pd.DataFrame({
            'user_session': test_sessions,
            'session_value': test_predictions
        })
        
        # Transform back from log scale if needed
        # Note: This assumes log1p transformation was applied during preprocessing
        submission_df['session_value'] = np.expm1(submission_df['session_value'])
        
        # Save submission
        submission_df.to_csv('baseline_submission.csv', index=False)
        
        print(f"Predictions saved to baseline_submission.csv")
        print(f"Prediction statistics:")
        print(f"  Min: {submission_df['session_value'].min():.2f}")
        print(f"  Max: {submission_df['session_value'].max():.2f}")
        print(f"  Mean: {submission_df['session_value'].mean():.2f}")
        print(f"  Median: {submission_df['session_value'].median():.2f}")
        
        return submission_df
    
    def generate_comprehensive_report(self, comparison_df, best_model_name):
        """Generate comprehensive baseline model report"""
        print("\n" + "="*60)
        print("COMPREHENSIVE BASELINE MODEL REPORT")
        print("="*60)
        
        best_results = self.results[best_model_name]
        
        print(f"🏆 BEST PERFORMING MODEL: {best_model_name}")
        print(f"📊 PERFORMANCE METRICS:")
        print(f"  Validation MSE: {best_results['val_mse']:.6f}")
        print(f"  Validation R²: {best_results['val_r2']:.6f}")
        print(f"  Cross-validation MSE: {best_results['cv_mse']:.6f} ± {best_results['cv_std']:.6f}")
        print(f"  Training MSE: {best_results['train_mse']:.6f}")
        print(f"  Overfitting Score: {best_results['overfitting']:.6f}")
        
        # Model complexity analysis
        print(f"\n🔍 MODEL ANALYSIS:")
        if best_results['overfitting'] > 0.01:
            print("  ⚠️  High overfitting detected - consider regularization")
        elif best_results['overfitting'] < -0.01:
            print("  ⚠️  Underfitting detected - consider more complex model")
        else:
            print("  ✅ Good bias-variance balance")
        
        if best_results['cv_std'] > 0.01:
            print("  ⚠️  High cross-validation variance - model may be unstable")
        else:
            print("  ✅ Stable cross-validation performance")
        
        # Performance ranking
        print(f"\n📈 MODEL RANKING:")
        for i, row in comparison_df.head(5).iterrows():
            status = "🏆" if i == 0 else f"{i+1}."
            print(f"  {status} {row['Model']}: MSE = {row['Validation MSE']:.6f}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if best_results['val_r2'] > 0.9:
            print("  🎯 Excellent baseline performance achieved!")
            print("  📈 Consider advanced models (XGBoost, Neural Networks)")
            print("  🔧 Focus on feature engineering and hyperparameter tuning")
        elif best_results['val_r2'] > 0.8:
            print("  ✅ Good baseline performance")
            print("  📊 Try ensemble methods and feature selection")
            print("  🎛️  Hyperparameter optimization recommended")
        else:
            print("  ⚠️  Baseline performance needs improvement")
            print("  🔍 Review feature engineering and data preprocessing")
            print("  🧪 Try different model architectures")
        
        print(f"\n🎯 COMPETITION READINESS:")
        print(f"  MSE Score: {best_results['val_mse']:.6f}")
        print(f"  Model Complexity: {'Simple' if best_model_name in ['Linear Regression', 'Decision Tree'] else 'Moderate'}")
        print(f"  Interpretability: {'High' if best_model_name in ['Linear Regression', 'Decision Tree'] else 'Medium'}")
        print(f"  Training Time: {'Fast' if best_model_name != 'Support Vector Regression' else 'Slow'}")
        
        return {
            'best_model': best_model_name,
            'best_mse': best_results['val_mse'],
            'best_r2': best_results['val_r2'],
            'model_count': len(self.results),
            'comparison_df': comparison_df
        }

def main():
    """Main baseline model evaluation pipeline"""
    print("BTK DATATHON 2025 - BASELINE MODEL EVALUATION")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = BaselineModelEvaluator()
    
    # Load data
    train_df, test_df, feature_names = evaluator.load_preprocessed_data()
    
    # Prepare data
    data = evaluator.prepare_data_for_modeling(train_df, test_df, feature_names)
    
    # Initialize models
    models = evaluator.initialize_baseline_models()
    
    # Evaluate all models
    results = evaluator.evaluate_all_models(data)
    
    # Create performance comparison
    comparison_df = evaluator.create_performance_comparison()
    
    # Create visualizations
    best_model_name = evaluator.create_visualizations(comparison_df, data)
    
    # Generate predictions
    submission_df = evaluator.generate_predictions(data, best_model_name)
    
    # Generate comprehensive report
    final_report = evaluator.generate_comprehensive_report(comparison_df, best_model_name)
    
    # Save results
    comparison_df.to_csv('baseline_model_comparison.csv', index=False)
    
    print(f"\n🎉 Baseline model evaluation complete!")
    print(f"📁 Files saved: baseline_submission.csv, baseline_model_comparison.csv")
    
    return {
        'evaluator': evaluator,
        'results': results,
        'comparison_df': comparison_df,
        'submission_df': submission_df,
        'final_report': final_report,
        'best_model': best_model_name
    }

if __name__ == "__main__":
    baseline_results = main()
